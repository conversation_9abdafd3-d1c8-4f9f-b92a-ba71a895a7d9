import os
import shutil
import time
from distutils.core import setup
from pathlib import Path

from Cython.Build import cythonize

# 设置基本参数
start_time = time.time()
curr_dir = Path().resolve()
apps_dir = curr_dir / 'apps'
build_dir = curr_dir / 'build_linux'
build_tmp_dir = build_dir / 'temp'

migrations_dir = 'migrations'


def get_py_files(base_path, parent_path=''):
    """
    获取指定目录下的 .py 文件的绝对路径，但排除 __init__.py 文件和 migrations 目录。

    :param base_path: 根路径
    :param parent_path: 父路径
    :return: .py 文件的绝对路径生成器
    """
    full_path = base_path / parent_path

    for item in full_path.iterdir():
        if item.is_dir() and item.name != migrations_dir and not item.name.startswith('.'):
            yield from get_py_files(base_path, item.relative_to(base_path))
        elif item.is_file() and not item.name.startswith('.'):
            if item.suffix == '.py' and item.name != '__init__.py':
                yield str(item.resolve())  # 将 Path 转换为字符串路径


def copy_files(base_path, dest_dir, filter_func=None):
    """复制符合条件的文件到目标目录"""
    for root, _, files in os.walk(base_path):
        for file in files:
            source_path = Path(root) / file
            relative_path = Path(root).relative_to(base_path)
            dest_path = dest_dir / relative_path / file

            if filter_func is None or filter_func(source_path, root):
                dest_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy(source_path, dest_path)


def delete_c_files(base_path):
    """递归删除 .c 文件"""
    for c_file in Path(base_path).rglob('*.c'):
        c_file.unlink()


def compile_files():
    """对 apps 目录中的 Python 文件进行编译，并将结果输出到根目录下的 build 目录中"""
    module_list = list(get_py_files(apps_dir))

    try:
        setup(
            ext_modules=cythonize(module_list, compiler_directives={'language_level': "3"}, nthreads=os.cpu_count()),
            script_args=["build_ext", "-b", str(build_dir), "-t", str(build_tmp_dir)],
        )
    except Exception as ex:
        print("Compilation error:", ex)

    print("Compilation complete! Time elapsed:", time.time() - start_time, 'seconds')


if __name__ == '__main__':
    try:
        # 确保输出目录存在
        if build_dir.exists():
            shutil.rmtree(build_dir)
        build_dir.mkdir(parents=True, exist_ok=True)
        # 编译文件
        compile_files()
        # 处理其余文件
        delete_c_files(apps_dir)
        copy_files(apps_dir, build_dir / 'apps',
                   lambda f, root: f.name == '__init__.py' or not f.suffix in {'.py', '.pyc', '.pyx'})
        copy_files(apps_dir, build_dir / 'apps', lambda f, root: migrations_dir in Path(root).parts)
    except Exception as e:
        print("Error during execution:", e)
    finally:
        delete_c_files(apps_dir)
        if build_tmp_dir.exists():
            shutil.rmtree(build_tmp_dir)
