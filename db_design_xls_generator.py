#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File: db_design_xls_generator.py
@Author: JT_DEV
@LastCommit: 
"""
import os
import django
from django.apps import apps
from openpyxl import Workbook
from openpyxl.styles import Border, Side, Font
from openpyxl.utils import get_column_letter

# 创建映射字典
type_mapping = {
    "AutoField": "INT",
    "BigAutoField": "BIGINT",
    "BigIntegerField": "BIGINT",
    "BinaryField": "BLOB",
    "BooleanField": "BOOL",
    "CharField": "VARCHAR",
    "DateField": "DATE",
    "DateTimeField": "DATETIME",
    "DecimalField": "DECIMAL",
    "DurationField": "TIME",
    "FileField": "VARCHAR",
    "FilePathField": "VARCHAR",
    "FloatField": "FLOAT",
    "IntegerField": "INT",
    "<PERSON>ricIP<PERSON>ddressField": "VARCHAR",
    "ImageField": "VARCHA<PERSON>",
    "J<PERSON><PERSON>ield": "JSO<PERSON>",
    "Null<PERSON><PERSON>eanField": "BOOL",
    "PositiveBigIntegerField": "BIGINT",
    "PositiveIntegerField": "INT",
    "PositiveSmallIntegerField": "SMALLINT",
    "SlugField": "VARCHAR",
    "SmallAutoField": "SMALLINT",
    "SmallIntegerField": "SMALLINT",
    "TextField": "TEXT",
    "TimeField": "TIME",
    "URLField": "VARCHAR",
    "UUIDField": "VARCHAR",
    "ForeignKey": "INT",
    "OneToOneField": "INT",
    "ManyToManyField": "INT",
    "ManyToManyRel": "INT",
    "ManyToOneRel": "INT",
    "OneToOneRel": "INT",
    "GenericRel": "INT",
    "ArrayField": "ARRAY",
    "BigIntegerRangeField": "BIGINT",
    "CICharField": "VARCHAR",
    "CIEmailField": "VARCHAR",
    "CITextField": "TEXT",
    "EmailField": "VARCHAR",
    "IPAddressField": "VARCHAR",
    "PhoneField": "VARCHAR",
}

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "jijian_jiancha.settings")
django.setup()

# 创建一个新的 Excel 工作簿
wb = Workbook()

# 定义边框样式
thin_border = Border(
    left=Side(style="thin"),
    right=Side(style="thin"),
    top=Side(style="thin"),
    bottom=Side(style="thin"),
)

# 获取所有模型
all_table_name = []

# 排除 Django 自带的模型
excluded_apps = [
    "auth",
    "contenttypes",
    "sessions",
    "admin",
    "sites",
    "messages",
    "staticfiles",
    "sessions",
    "admin",
    "django.contrib",
    "django_celery_results",
]

for model in apps.get_models():
    # 如果模型的 app_label 在排除列表中，跳过该模型
    if model._meta.app_label in excluded_apps:
        continue
    # 为每个模型创建一个新的工作表
    sheet = wb.create_sheet(title=model._meta.object_name)

    # 添加标题
    sheet["A1"] = "字段名称"
    sheet["B1"] = "字段类型"
    sheet["C1"] = "是否为空"
    sheet["D1"] = "是否主键"
    sheet["E1"] = "详细名称"

    # 设置标题行加粗
    for col in range(1, 6):
        sheet.cell(row=1, column=col).font = Font(bold=True)
        sheet.cell(row=1, column=col).border = thin_border

    row_num = 2  # 从第二行开始填写数据

    all_table_name.append(f"{model._meta.db_table} [{model._meta.verbose_name}]")

    # 循环遍历模型的字段，并将信息写入 Excel 表格
    for field in model._meta.fields:
        sheet[f"A{row_num}"] = field.name
        sheet[f"B{row_num}"] = type_mapping.get(
            field.get_internal_type(), field.get_internal_type()
        )
        sheet[f"C{row_num}"] = "√" if field.null else ""
        sheet[f"D{row_num}"] = "√" if field.primary_key else ""
        sheet[f"E{row_num}"] = str(field.verbose_name)

        # 为每个单元格添加边框
        for col in range(1, 6):
            sheet.cell(row=row_num, column=col).border = thin_border

        row_num += 1

    # 自动调整列宽
    for col in range(1, 6):
        col_letter = get_column_letter(col)
        max_length = 0
        for row in sheet.iter_rows(min_col=col, max_col=col):
            for cell in row:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(cell.value)
                except:
                    pass
        sheet.column_dimensions[col_letter].width = max_length + 2

# 删除默认创建的 "Sheet" 工作表
del wb["Sheet"]

# 保存Excel文件
wb.save("广东分署-纪检监察数据库设计文档.xlsx")
print("数据库设计文档生成成功！")
