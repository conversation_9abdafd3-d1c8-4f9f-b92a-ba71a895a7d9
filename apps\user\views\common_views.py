#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File     : common_views.py
<AUTHOR> JT_DA
@Date     : 2025/04/15
@File_Desc: 
'''

from rest_framework.views import APIView

from internal_control.settings import WORKBENCH_FRONTEND_URL
from utils.ajax_result import AjaxResult


class GetMainMenuUrl(APIView):

    def get(self, request):
        data = WORKBENCH_FRONTEND_URL
        return AjaxResult.success(data=data)