[group:internal_control_gdfs]
programs=gunicorn_internal_control_gdfs,celery_worker_internal_control_gdfs,celery_beat_internal_control_gdfs

[program:gunicorn_internal_control_gdfs]
command=bash -c "source /root/miniconda3/bin/activate env_py310_hg && gunicorn internal_control.wsgi:application -c gunicorn_config.py"  # 指定要运行的程序的命令
directory=/mnt/d/pythonProject/internal_control_gdfs_oauth  # 指定程序的工作目录
autostart=false  # 是否随 Supervisor 启动自动启动该进程
autorestart=true  # 是否在进程退出时自动重启
stderr_logfile=/mnt/d/pythonProject/internal_control_gdfs_oauth/logs/gunicorn.err.log  # 错误日志文件路径
stdout_logfile=/mnt/d/pythonProject/internal_control_gdfs_oauth/logs/gunicorn.out.log  # 输出日志文件路径
user=root  # 运行进程的用户
environment=DM_HOME=/workdir/dm8,LD_LIBRARY_PATH="$LD_LIBRARY_PATH:/workdir/dm8/bin:/workdir/dm8/drivers/dpi/"

[program:celery_worker_internal_control_gdfs]
command=bash -c "source /root/miniconda3/bin/activate env_py310_hg && celery -A internal_control worker --loglevel=info"  # 指定要运行的程序的命令
directory=/mnt/d/pythonProject/internal_control_gdfs_oauth  # 指定程序的工作目录
autostart=false  # 是否随 Supervisor 启动自动启动该进程
autorestart=true  # 是否在进程退出时自动重启
stderr_logfile=/mnt/d/pythonProject/internal_control_gdfs_oauth/logs/worker.err.log  # 错误日志文件路径
stdout_logfile=/mnt/d/pythonProject/internal_control_gdfs_oauth/logs/worker.out.log  # 输出日志文件路径
user=root  # 运行进程的用户
environment=DM_HOME=/workdir/dm8,LD_LIBRARY_PATH="$LD_LIBRARY_PATH:/workdir/dm8/bin:/workdir/dm8/drivers/dpi/"

[program:celery_beat_internal_control_gdfs]
command=bash -c "source /root/miniconda3/bin/activate env_py310_hg && celery -A internal_control beat --loglevel=info"  # 指定要运行的程序的命令
directory=/mnt/d/pythonProject/internal_control_gdfs_oauth  # 指定程序的工作目录
autostart=false  # 是否随 Supervisor 启动自动启动该进程
autorestart=true  # 是否在进程退出时自动重启
stderr_logfile=/mnt/d/pythonProject/internal_control_gdfs_oauth/logs/beat.err.log  # 错误日志文件路径
stdout_logfile=/mnt/d/pythonProject/internal_control_gdfs_oauth/logs/beat.out.log  # 输出日志文件路径
user=root  # 运行进程的用户
environment=DM_HOME=/workdir/dm8,LD_LIBRARY_PATH="$LD_LIBRARY_PATH:/workdir/dm8/bin:/workdir/dm8/drivers/dpi/"