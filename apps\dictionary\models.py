from django.db import models


# 迁移语句
# python manage.py makemigrations
# python manage.py migrate
class DictType(models.Model):
    type_code = models.CharField(max_length=50, unique=True)
    type_name = models.Char<PERSON>ield(max_length=100)

    class Meta:
        verbose_name = "字典类型"
        verbose_name_plural = "字典类型"
        db_table = "t_md_type"

    def __str__(self):
        return self.type_name


class DictItem(models.Model):
    dict_type = models.ForeignKey(DictType, on_delete=models.CASCADE, related_name="items")
    parent = models.ForeignKey("self", on_delete=models.CASCADE, null=True, blank=True, related_name="children")
    dict_code = models.Char<PERSON>ield(max_length=50)
    dict_info = models.CharField(max_length=255)
    email = models.EmailField(max_length=254, null=True, blank=True, verbose_name="邮箱地址")

    class Meta:
        verbose_name = "字典明细"
        verbose_name_plural = "字典明细"
        db_table = "t_md_item"

    def __str__(self):
        return self.dict_info
