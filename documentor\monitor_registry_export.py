#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : monitor_registry_export.py
<AUTHOR> JT_DA
@Date     : 2025/04/24
@File_Desc:
"""

from utils.pyxl_helper import XlsxHelper


def main(data, source_path):
    """

    :param data:
    :param source_path:
    :return:
    """
    col_set = {
        "monitor_project": "B",
        "monitor_point": "C",
        "monitor_method": "D",
        "monitor_registry_development_cn": "E",
        "responsible_department_cn": "F",
        "monitor_level_cn": "G",
        "monitor_switch_cn": "H",
        "monitor_cycle_cn": "I",
        "file_cn": "J",
    }

    start_row_idx = 3
    xh = "A"
    xlsx_helper = XlsxHelper(source_path=source_path)
    xlsx_helper.data_write_in_sheet(data, col_set, start_row_idx, xh)
    xlsx_helper.xlsx_styles(start_row_idx, ["B", "C", "D", "E", "G", "J"])

    return xlsx_helper.write_buffer()
