# 使用一个适用的 Python 镜像作为基础镜像
FROM python:3.10.16-slim-bullseye

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    DM_HOME=/dm8 \
    LD_LIBRARY_PATH="$LD_LIBRARY_PATH:/dm8/bin:/dm8/drivers/dpi/"

# 复制requirements.txt文件
COPY requirements.txt /app/

# 替换sources.list使用清华大学源并安装依赖
RUN sed -i 's/deb.debian.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apt/sources.list && \
    sed -i 's/security.debian.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apt/sources.list && \
    apt-get update && \
    apt-get install -y --no-install-recommends \
    python3-dev \
    vim \
    default-libmysqlclient-dev \
    supervisor \
    build-essential \
    pkg-config && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* && \
    python -m pip install -i https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple --upgrade pip && \
    pip install --no-cache-dir -i https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple -r requirements.txt

# 复制应用程序代码到工作目录
COPY . /app/

# 复制supervisor配置
COPY documents/supervisor_conf/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# 编译应用，移动编译后的文件，清理不必要的文件，并确保启动脚本可执行
RUN python compile_apps.py && \
    rm -rf /app/apps && \
    mv /app/build_linux/apps /app/apps && \
    rm -rf /app/build_linux && \
    find /app -name "*.pyc" -delete && \
    find /app -name "__pycache__" -delete && \
    chmod +x /app/start.sh

# 启动脚本
CMD ["bash", "/app/start.sh"]