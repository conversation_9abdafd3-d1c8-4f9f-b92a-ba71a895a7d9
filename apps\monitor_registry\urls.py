#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : urls.py
<AUTHOR> JT_DA
@Date     : 2025/04/18
@File_Desc:
"""

from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer

from apps.monitor_registry.views import (
    monitor_registry_view_set,
    monitor_registry_development_view_set,
    monitor_registry_import,
)

router = DefaultRouter()
router.register(r"monitor_registry", monitor_registry_view_set.MonitorRegistryViewSet, basename="monitor_registry")
router.register(
    r"monitor_registry_development",
    monitor_registry_development_view_set.MonitorRegistryDevelopmentViewSet,
    basename="monitor_registry_development",
)

urlpatterns = [
    path("", include(router.urls)),
    path("monitor_registry_export/", monitor_registry_view_set.MonitorRegistryExport.as_view({"get": "list"})),
    path("monitor_registry_template/", monitor_registry_import.MonitorRegistryImportTemplate.as_view()),
    path("monitor_registry_import/", monitor_registry_import.MonitorRegistryBatchImport.as_view()),
]
