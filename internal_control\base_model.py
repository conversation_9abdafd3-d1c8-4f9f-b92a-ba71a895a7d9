#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : base_model.py
<AUTHOR> JT_DA
@Date     : 2025/04/18
@File_Desc:
"""

from django.db import models


class BaseModel(models.Model):
    revision = models.IntegerField(default=1, verbose_name="版本号")
    created_by = models.IntegerField(null=True, verbose_name="创建人")
    created_time = models.DateTimeField(blank=True, null=True, auto_now_add=True, verbose_name="创建时间")
    updated_by = models.IntegerField(null=True, verbose_name="更新人")
    updated_time = models.DateTimeField(blank=True, null=True, auto_now=True, verbose_name="更新时间")

    class Meta:
        abstract = True

    def __str__(self):
        return str(self.id)

    def to_dict(self):
        return self.__dict__
