#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : tasks.py
<AUTHOR> JT_DA
@Date     : 2025/04/15
@File_Desc:
"""

import math
import requests

from apps.user.models import SystemUser, SystemGroup
from internal_control.celery import app, CeleryResult
from internal_control.settings import AUTH_SERVER_URL


@app.task()
def sync_auth_user_local():
    try:
        url = f"{AUTH_SERVER_URL}/user/"
        headers = {"Authorization": ""}
        
        # 首次请求获取总数量
        params = {"page": 1, "page_size": 100}
        response = requests.get(url, headers=headers, params=params)
        response_data = response.json().get("data", {})
        
        count = response_data.get("count", 0)
        total_pages = math.ceil(count / 100) if count > 0 else 1
        
        # 汇总所有页面的用户数据
        all_auth_system_user_info = []
        all_auth_system_user_info.extend(response_data.get("results", []))
        
        # 请求剩余页面的数据
        for page in range(2, total_pages + 1):
            params = {"page": page, "page_size": 100}
            response = requests.get(url, headers=headers, params=params)
            page_results = response.json().get("data", {}).get("results", [])
            all_auth_system_user_info.extend(page_results)

        all_username_lst = []

        for user_info in all_auth_system_user_info:
            if user_info.get("username") == "admin":
                continue

            role = user_info.get("role")[0] if user_info.get("role") else None
            group = user_info.get("department")[0] if user_info.get("department") else None

            clean_user_info = {
                "id": user_info.get("id"),
                "username": user_info.get("username"),
                "real_name": user_info.get("display_name"),
                "is_active": user_info.get("is_active"),
                "is_staff": user_info.get("is_staff"),
                "is_superuser": user_info.get("is_superuser"),
                "all_path_name": user_info.get("all_path_name"),
                "mobile": user_info.get("mobile"),
                "email": user_info.get("email"),
                "cardid": user_info.get("cardid"),
                "group_id": group.get("id") if group else None,
                "group_name": group.get("name") if group else None,
                "role_id": role.get("id") if role else None,
                "role_name": role.get("name") if role else None,
            }

            username = clean_user_info.pop("username")
            SystemUser.objects.update_or_create(username=username, defaults=clean_user_info)
            all_username_lst.append(username)

        if all_username_lst:
            SystemUser.objects.exclude(username__in=all_username_lst).delete()

        return CeleryResult.success("sync_auth_user_local")
    except Exception as e:
        return CeleryResult.fail("sync_auth_user_local", str(e))


@app.task()
def sync_auth_group_local():
    try:
        url = f"{AUTH_SERVER_URL}/department/"
        headers = {"Authorization": ""}
        params = {"page": 1, "page_size": 5000}
        response = requests.get(url, headers=headers, params=params)
        all_auth_system_group_info = response.json().get("data", {}).get("results", [])

        all_group_id_lst = []

        for group_info in all_auth_system_group_info:
            clean_group_info = {"id": group_info.get("id"), "name": group_info.get("name")}
            pk = clean_group_info.pop("id")
            SystemGroup.objects.update_or_create(id=pk, defaults=clean_group_info)
            all_group_id_lst.append(pk)

        if all_group_id_lst:
            SystemGroup.objects.exclude(id__in=all_group_id_lst).delete()

        return CeleryResult.success("sync_auth_group_local")
    except Exception as e:
        return CeleryResult.fail("sync_auth_group_local", str(e))
