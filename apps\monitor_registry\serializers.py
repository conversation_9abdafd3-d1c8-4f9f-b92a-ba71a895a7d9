#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : serializers.py
<AUTHOR> JT_DA
@Date     : 2025/04/18
@File_Desc:
"""
from datetime import datetime
from rest_framework import serializers

from apps.dictionary.models import DictItem
from apps.monitor_registry.models import MonitorRegistry, MonitorRegistryDevelopment, MonitorRegistryFile
from utils.file_helper import build_preview_url


class MonitorRegistryFileSerializer(serializers.ModelSerializer):
    file_preview_url = serializers.SerializerMethodField(help_text="文件预览地址")
    method_name = serializers.CharField(default="monitor_registry", help_text="下载文件的方法名")

    class Meta:
        model = MonitorRegistryFile
        fields = ("id", "file_name", "file_preview_url", "method_name")

    def get_file_preview_url(self, obj):
        return build_preview_url("monitor_registry", obj.id, obj.file.url)


class MonitorRegistryDevelopmentSerializer(serializers.ModelSerializer):
    monitor_project = serializers.SerializerMethodField(help_text="监控项目名称")
    development_date = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S")

    class Meta:
        model = MonitorRegistryDevelopment
        exclude = ("revision", "created_by", "created_time", "updated_by", "updated_time")
        read_only_fields = ("id", "monitor_project")

    def get_monitor_project(self, obj):
        if obj.monitor_registry:
            return obj.monitor_registry.monitor_project
        return ""

    def validate(self, data):
        """
        验证数据中是否包含monitor_registry字段
        """
        if not data.get("monitor_registry"):
            raise serializers.ValidationError({"monitor_registry": "监控列表ID不能为空"})
        return data


class MonitorRegistrySerializer(serializers.ModelSerializer):
    attachment = MonitorRegistryFileSerializer(many=True, read_only=True)
    monitor_registry_development = MonitorRegistryDevelopmentSerializer(many=True, read_only=True)

    responsible_department_cn = serializers.SerializerMethodField(help_text="负责部门（中文描述）")
    monitor_level_cn = serializers.SerializerMethodField(help_text="监控层级（中文描述）")
    monitor_switch_cn = serializers.SerializerMethodField(help_text="监控开关（中文描述）")
    monitor_cycle_cn = serializers.SerializerMethodField(help_text="监控周期（中文描述）")
    monitor_registry_development_cn = serializers.SerializerMethodField(help_text="开展情况（中文描述）")
    file_cn = serializers.SerializerMethodField(help_text="附件（中文描述）")

    class Meta:
        model = MonitorRegistry
        exclude = ("revision", "created_by", "created_time", "updated_by", "updated_time")
        read_only_fields = (
            "id",
            "responsible_department_cn",
            "monitor_level_cn",
            "monitor_switch_cn",
            "monitor_cycle_cn",
            "monitor_registry_development_cn",
            "file_cn",
        )

    def get_responsible_department_cn(self, obj):
        if obj.responsible_department:
            return DictItem.objects.get(pk=obj.responsible_department.id).dict_info
        return ""

    def get_monitor_level_cn(self, obj):
        if obj.monitor_level:
            return DictItem.objects.get(pk=obj.monitor_level.id).dict_info
        return ""

    def get_monitor_switch_cn(self, obj):
        return "启用" if obj.monitor_switch else "关闭"

    def get_monitor_cycle_cn(self, obj):
        if obj.monitor_cycle:
            return DictItem.objects.get(pk=obj.monitor_cycle.id).dict_info
        return ""

    def get_monitor_registry_development_cn(self, obj):
        serializer = MonitorRegistryDevelopmentSerializer(
            obj.monitor_registry_development.all().order_by("-development_date"), many=True
        )
        formatted_data = []
        for item in serializer.data:
            date = item["development_date"]
            if isinstance(date, str):
                date = datetime.strptime(date, "%Y-%m-%d %H:%M:%S")
            date = date.strftime("%Y-%m-%d")
            content = item["development_content"]
            formatted_data.append(f"{date} {content}")
        return "\n".join(formatted_data)

    def get_file_cn(self, obj):
        # Serialize the related ProjectJobFile instances
        serializer = MonitorRegistryFileSerializer(obj.attachment.all(), many=True)
        serialized_data = serializer.data

        # Check if there are any files attached
        if serialized_data:
            # If there's only one file, return its name directly
            if len(serialized_data) == 1:
                return serialized_data[0]["file_name"]
            else:
                # If there are multiple files, concatenate their names with their index
                return "\n".join([f"{idx + 1}、{item['file_name']}" for idx, item in enumerate(serialized_data)])
        else:
            return ""
