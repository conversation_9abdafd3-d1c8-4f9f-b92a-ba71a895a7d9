# Generated by Django 4.1.13 on 2025-04-23 15:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("monitor_registry", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="MonitorRegistryFile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("revision", models.IntegerField(default=1, verbose_name="版本号")),
                ("created_by", models.IntegerField(null=True, verbose_name="创建人")),
                (
                    "created_time",
                    models.DateTimeField(
                        auto_now_add=True, null=True, verbose_name="创建时间"
                    ),
                ),
                ("updated_by", models.IntegerField(null=True, verbose_name="更新人")),
                (
                    "updated_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="更新时间"
                    ),
                ),
                ("file", models.FileField(upload_to="./upload", verbose_name="附件")),
                (
                    "file_name",
                    models.CharField(max_length=255, verbose_name="附件原名"),
                ),
            ],
            options={
                "verbose_name": "监控列表附件",
                "verbose_name_plural": "监控列表附件",
                "db_table": "t_monitor_registry_file",
            },
        ),
        migrations.AddField(
            model_name="monitorregistry",
            name="attachment",
            field=models.ManyToManyField(
                blank=True,
                related_name="monitor_registry_attachment",
                to="monitor_registry.monitorregistryfile",
                verbose_name="附件",
            ),
        ),
    ]
