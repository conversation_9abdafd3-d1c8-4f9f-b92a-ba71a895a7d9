#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : user_helper.py
<AUTHOR> JT_DA
@Date     : 2025/04/15
@File_Desc:
"""

import requests

from internal_control.constant import RedisKey
from internal_control.settings import AUTH_SERVER_URL, SYSTEM_NAME
from utils.cache_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>


def request_auth_user_info(request, auth_token=None):
    auth_user_info_cache = CacheManager(RedisKey.AUTH_USER_INFO)

    auth_header = auth_token if auth_token else request.headers.get("Authorization")

    if not auth_header:
        return {}

    user_info = auth_user_info_cache.get(auth_header)
    if user_info:
        return user_info

    response = requests.get(f"{AUTH_SERVER_URL}/info/", headers={"Authorization": auth_header})
    auth_system_user_info = response.json().get("data")

    role = auth_system_user_info.get("role")[0] if auth_system_user_info.get("role") else {}
    group = auth_system_user_info.get("department")[0] if auth_system_user_info.get("department") else {}

    user_info = {
        "user_id": auth_system_user_info.get("id"),
        "username": auth_system_user_info.get("username"),
        "is_staff": auth_system_user_info.get("is_staff"),
        "role_id": role.get("id", ""),
        "role_name": role.get("name", ""),
        "group_id": group.get("id", ""),
        "group_name": group.get("name", ""),
        "real_name": auth_system_user_info.get("display_name"),
        "user_all_permissions": auth_system_user_info.get("user_all_permissions", {}).get(f"{SYSTEM_NAME}", []),
    }

    auth_user_info_cache.set(auth_header, user_info)
    return user_info


def get_h4a_user_info(request, params):
    auth_token = request.headers.get("Authorization")
    if not auth_token:
        return {}

    response = requests.get(
        f"{AUTH_SERVER_URL}/h4a/h4a_user_info/",
        headers={"Authorization": auth_token},
        params=params,
    )
    all_h4a_user_info = response.json().get("data", {}).get("results", [])

    clean_h4a_user_lst = [
        {
            "username": user_info.get("user_guid"),
            "name": user_info.get("display_name"),
            "company": "\\".join(
                [item for item in user_info.get("all_path_name", "").split("\\") if item.endswith("海关")]
            ),
            "is_personnel": 1,
        }
        for user_info in all_h4a_user_info
    ]

    return clean_h4a_user_lst
