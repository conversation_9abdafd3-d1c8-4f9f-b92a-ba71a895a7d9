#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : email_helper.py
<AUTHOR> JT_DA
@Date     : 2025/06/24
@File_Desc: 邮件发送辅助函数
"""

import smtplib
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Header
from django.conf import settings

logger = logging.getLogger(__name__)


def send_email(sender_email, receiver_email, subject, content, smtp_server, smtp_port, username, password):
    """
    通过SMTP发送邮件

    :param sender_email: 发件人邮箱
    :param receiver_email: 收件人邮箱
    :param subject: 邮件主题
    :param content: 邮件内容
    :param smtp_server: SMTP服务器地址
    :param smtp_port: SMTP服务器端口
    :param username: SMTP用户名
    :param password: SMTP密码
    :return: (success: bool, error_message: str)
    """
    try:
        # 创建邮件对象
        msg = MIMEMultipart()
        msg["From"] = sender_email
        msg["To"] = receiver_email
        msg["Subject"] = Header(subject, "utf-8")

        # 添加邮件正文
        msg.attach(MIMEText(content, "plain", "utf-8"))

        # 连接SMTP服务器
        server = smtplib.SMTP(smtp_server, smtp_port)
        if smtp_server == "smtp.qq.com":
            server.starttls()  # 启用TLS加密（仅QQ邮箱）
        server.login(username, password)

        # 发送邮件
        text = msg.as_string()
        server.sendmail(sender_email, receiver_email, text)
        server.quit()

        logger.info(f"邮件发送成功: {sender_email} -> {receiver_email}, 主题: {subject}")
        return True, None

    except smtplib.SMTPAuthenticationError as e:
        error_msg = f"SMTP认证失败: {str(e)}"
        logger.error(error_msg)
        return False, error_msg

    except smtplib.SMTPRecipientsRefused as e:
        error_msg = f"收件人被拒绝: {str(e)}"
        logger.error(error_msg)
        return False, error_msg

    except smtplib.SMTPServerDisconnected as e:
        error_msg = f"SMTP服务器连接断开: {str(e)}"
        logger.error(error_msg)
        return False, error_msg

    except smtplib.SMTPException as e:
        error_msg = f"SMTP异常: {str(e)}"
        logger.error(error_msg)
        return False, error_msg

    except Exception as e:
        error_msg = f"邮件发送异常: {str(e)}"
        logger.error(error_msg)
        return False, error_msg


def send_email_with_settings(receiver_email, subject, content):
    """
    使用系统配置发送邮件

    :param receiver_email: 收件人邮箱
    :param subject: 邮件主题
    :param content: 邮件内容
    :return: (success: bool, error_message: str)
    """
    try:
        smtp_server = settings.CUSTOMS_EMAIL.get("SMTP_SERVER")
        smtp_port = settings.CUSTOMS_EMAIL.get("SMTP_PORT")
        sender_email = settings.CUSTOMS_EMAIL.get("SENDER_EMAIL")
        username = settings.CUSTOMS_EMAIL.get("USERNAME")
        password = settings.CUSTOMS_EMAIL.get("PASSWORD")

        # 验证必要的配置参数
        if not all([smtp_server, smtp_port, sender_email, username, password]):
            error_msg = "邮件配置不完整，请检查CUSTOMS_EMAIL设置"
            logger.error(error_msg)
            return False, error_msg

        return send_email(
            sender_email,
            receiver_email,
            subject,
            content,
            smtp_server,
            smtp_port,
            username,
            password,
        )

    except Exception as e:
        error_msg = f"邮件发送配置异常: {str(e)}"
        logger.error(error_msg)
        return False, error_msg


if __name__ == "__main__":
    """
    测试send_email函数
    """
    import os
    import sys
    import django
    
    # 添加项目根目录到Python路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    
    # 设置Django环境
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'internal_control.settings')
    django.setup()
    
    print("=== 邮件发送函数测试 ===")
    
    # 从配置中获取SMTP参数
    smtp_server = settings.CUSTOMS_EMAIL.get("SMTP_SERVER")
    smtp_port = settings.CUSTOMS_EMAIL.get("SMTP_PORT")
    sender_email = settings.CUSTOMS_EMAIL.get("SENDER_EMAIL")
    username = settings.CUSTOMS_EMAIL.get("USERNAME")
    password = settings.CUSTOMS_EMAIL.get("PASSWORD")
    
    print(f"SMTP服务器: {smtp_server}")
    print(f"SMTP端口: {smtp_port}")
    print(f"发件人邮箱: {sender_email}")
    print(f"用户名: {username}")
    print(f"密码: {'***' if password else '未设置'}")
    
    # 测试参数
    test_receiver = "<EMAIL>"  # 可以替换为真实邮箱进行测试
    test_subject = "内控监控系统邮件发送测试"
    test_content = """
这是一封来自内控监控系统的测试邮件。

测试时间: 2025年6月24日
测试功能: SMTP邮件发送

本期尚有3个监控项目未更新，具体如下：
    财务处 - 资金管理监控
    人事处 - 人员调动审批监控  
    采购处 - 采购合同管理监控

请予以关注。

此邮件为系统测试邮件，请忽略。
    """
    
    print(f"\n收件人: {test_receiver}")
    print(f"主题: {test_subject}")
    print(f"内容预览: {test_content.strip()[:100]}...")
    
    # 提示用户是否要真实发送
    print("\n⚠️  注意：这将发送真实邮件！")
    user_input = input("是否要发送测试邮件？(输入 'yes' 确认发送，其他任意键取消): ")
    
    if user_input.lower() == 'yes':
        print("\n开始发送测试邮件...")
        success, error_message = send_email(
            sender_email=sender_email,
            receiver_email=test_receiver,
            subject=test_subject,
            content=test_content,
            smtp_server=smtp_server,
            smtp_port=smtp_port,
            username=username,
            password=password,
        )
        
        if success:
            print("✅ 邮件发送成功！")
        else:
            print(f"❌ 邮件发送失败: {error_message}")
    else:
        print("\n📋 测试取消，未发送真实邮件")
        print("✅ 函数调用接口正常")
        print("✅ 参数读取正常")
        print("💡 如需测试真实发送，请修改 test_receiver 为有效邮箱地址")
    
    print("\n=== 测试完成 ===")

