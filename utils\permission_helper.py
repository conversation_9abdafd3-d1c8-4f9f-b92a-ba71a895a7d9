#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : permission_helper.py
<AUTHOR> JT_DA
@Date     : 2025/04/15
@File_Desc:
"""

from functools import wraps

import requests
from django.conf import settings
from ipware import get_client_ip
from rest_framework import permissions

from internal_control.settings import AUTH_SERVER_URL
from utils.ajax_result import AjaxResult


class MyPermission(permissions.BasePermission):
    def has_permission(self, request, view):
        if request.user and request.user.is_authenticated:
            return True
        client_ip, _ = get_client_ip(request)
        return client_ip in settings.IP_WHITE_LIST


class IPWhitelistPermission(permissions.BasePermission):
    INTERNAL_IP_PREFIXES = ("172", "10", "192.168")  # 添加常见的内网IP前缀

    def has_permission(self, request, view):
        client_ip, is_routable = get_client_ip(request)

        if not client_ip:
            return False

        # 检查是否为内网IP
        if any(client_ip.startswith(prefix) for prefix in self.INTERNAL_IP_PREFIXES):
            return True

        return client_ip in settings.IP_WHITE_LIST


def request_auth_has_perm(request, per):
    auth_header = request.headers.get("Authorization")
    url = f"{AUTH_SERVER_URL}/has_perm/"
    headers = {"Authorization": auth_header}
    querystring = {"permission": per}
    response = requests.get(url, params=querystring, headers=headers)
    return response.json().get("data", {}).get("has_permission", False)


def permission_required(per=None):
    def decorator(func):
        @wraps(func)
        def wrapper(self, request, *args, **kwargs):
            if isinstance(per, (list, tuple)):
                # 如果是数组,只要有一个权限通过即可
                for permission in per:
                    if request_auth_has_perm(request, permission):
                        return func(self, request, *args, **kwargs)
                return AjaxResult.forbidden()
            else:
                # 单个权限的情况
                if request_auth_has_perm(request, per):
                    return func(self, request, *args, **kwargs)
                else:
                    return AjaxResult.forbidden()

        return wrapper

    return decorator
