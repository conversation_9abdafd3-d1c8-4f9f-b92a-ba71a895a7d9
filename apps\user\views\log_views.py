#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : log_views.py
<AUTHOR> JT_DA
@Date     : 2025/04/15
@File_Desc:
"""

import requests
from ipware import get_client_ip
from rest_framework.views import APIView

from internal_control.settings import AUTH_SERVER_URL, SYSTEM_NAME
from utils.ajax_result import AjaxResult
from utils.user_helper import request_auth_user_info


# 操作日志
class OperationLog(APIView):

    def post(self, request):
        # 对接用户系统
        user_info = request_auth_user_info(request)
        username = user_info.get("username")
        real_name = user_info.get("real_name")

        # 1.获取参数
        button_name = request.data.get("button_name", None)  # 功能按钮名称
        button_type = request.data.get("button_type", None)  # 功能按钮类型
        page_url = request.data.get("page_url", None)  # 页面url
        page_plate = request.data.get("page_plate", None)  # 页面所属版块

        client_ip, _ = get_client_ip(request)
        try:
            auth_header = request.headers.get("Authorization")
            data = {
                "username": username,
                "account": real_name,
                "button": button_name,
                "kind": button_type,
                "url": page_url,
                "belong": f"{SYSTEM_NAME}：" + page_plate,
                "ip": client_ip,
            }
            url = f"{AUTH_SERVER_URL}/log/operation_log/"
            headers = {"Authorization": auth_header}
            _ = requests.request("POST", url, headers=headers, data=data)
            return AjaxResult.success()
        except Exception as e:
            return AjaxResult.fail()
