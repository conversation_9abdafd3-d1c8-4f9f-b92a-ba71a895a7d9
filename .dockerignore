# Custom
upload/
logs/
backups/
celerybeat*
.cunzhi-memory/

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Django stuff:
*.log
*.pot
*.pyc
*.pyo
*.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*,cover
.hypothesis/
.pytest_cache/
celerybeat-schedule
*.pid

# Translations
*.mo

# Mr Developer
.mr.developer.cfg
.project

# Backup files
*.bak
*.swp
*.swo
*~

# Env files
.env
.env.*

# Editors and IDEs
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# Testing
test_output/

# Pyre type checker
.pyre/
