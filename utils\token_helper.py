#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : token_helper.py
<AUTHOR> JT_DA
@Date     : 2025/04/15
@File_Desc:
"""

import base64
import json
from collections import OrderedDict

import requests
from ipware import get_client_ip
from rest_framework import exceptions, authentication

from apps.user.models import SystemUser
from internal_control import settings
from internal_control.constant import RedisKey
from utils.cache_manager import CacheManager
from apps.user.tasks import sync_auth_user_local


class MyOAuth2Authentication(authentication.BaseAuthentication):
    www_authenticate_realm = "api"

    def authenticate(self, request):
        # 当DEBUG为true且请求IP在白名单当中时的特殊处理
        if settings.DEBUG:
            client_ip, _ = get_client_ip(request)
            if client_ip in settings.IP_WHITE_LIST:
                try:
                    user = SystemUser.objects.get(real_name="毛棣")
                    user.is_authenticated = True
                    return user, "Bearer debug_token"
                except SystemUser.DoesNotExist:
                    pass

        token = self._get_token_from_header(request)
        if token:
            valid, user = self._verify_token(token)
            if valid:
                return user, f"Bearer {token}"
        raise exceptions.AuthenticationFailed("用户认证失败")

    def _get_token_from_header(self, request):
        auth_header = request.headers.get("Authorization", "").split()
        if len(auth_header) == 2 and auth_header[0].lower() == "bearer":
            return auth_header[1]

    def _verify_token(self, token):
        verified_token_cache = CacheManager(RedisKey.VERIFIED_TOKEN)
        username = verified_token_cache.get(token)
        if username:
            user = SystemUser.objects.get(username=username)
            user.is_authenticated = True
            return True, user

        oauth2_provider = getattr(settings, "OAUTH2_PROVIDER", {})
        introspection_url = oauth2_provider.get("RESOURCE_SERVER_INTROSPECTION_URL")
        client_id, client_secret = oauth2_provider.get("RESOURCE_SERVER_INTROSPECTION_CREDENTIALS")
        basic_auth = base64.b64encode(f"{client_id}:{client_secret}".encode("utf-8")).decode("utf-8")

        response = requests.post(
            introspection_url, data={"token": token}, headers={"Authorization": f"Basic {basic_auth}"}
        )
        if response.status_code == 200 and (data := json.loads(response.text)).get("active"):
            # 如果用户不存在，则同步用户
            if not SystemUser.objects.filter(username=data.get("username")).exists():
                _ = sync_auth_user_local()
            user = SystemUser.objects.get(username=data.get("username"))
            user.is_authenticated = True
            verified_token_cache.set(token, user.username)
            return True, user
        return False, None

    def authenticate_header(self, request):
        attributes = OrderedDict([("realm", self.www_authenticate_realm)])
        attributes.update(getattr(request, "oauth2_error", {}))
        return f"Bearer {self._dict_to_string(attributes)}"

    def _dict_to_string(self, my_dict):
        return ",".join([f'{k}="{v}"' for k, v in my_dict.items()])
