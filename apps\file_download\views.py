#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : views.py
<AUTHOR> JT_DA
@Date     : 2025/04/23
@File_Desc:
"""

from django.http import FileResponse
from rest_framework.generics import GenericAPIView

from apps.file_download.serializers import FileDownloadSerializer
from apps.file_download.serializers import choices
from utils.ajax_result import AjaxResult
from utils.permission_helper import IPWhitelistPermission


class FileDownloadView(GenericAPIView):
    """
    文件下载
    """

    authentication_classes = []
    permission_classes = [IPWhitelistPermission]

    serializer_class = FileDownloadSerializer

    def get(self, request, *args, **kwargs):

        serializer = self.get_serializer(data=kwargs)
        serializer.is_valid(raise_exception=True)
        method_name = serializer.validated_data.get("method_name")
        file_id = serializer.validated_data.get("file_id")

        for choice in choices:
            if choice[0] == method_name:
                get_file_func = choice[1]
                break
        else:
            return AjaxResult.fail(msg="文件类型不存在")

        try:
            _file_obj = get_file_func(file_id)
        except:
            return AjaxResult.fail(msg="文件不存在")

        file_path = _file_obj.file.path
        file_name = _file_obj.file_name if _file_obj.file_name else "downloaded_file"

        response = FileResponse(open(file_path, "rb"))
        encoded_file_name = file_name.encode("utf8").decode("ISO-8859-1")
        response["Content-Disposition"] = f"attachment;filename={encoded_file_name}"
        return response
