# Generated by Django 4.1.13 on 2025-07-09 15:27

import os
import pandas as pd
from django.db import migrations
from django.conf import settings


def load_email_recipients(apps, schema_editor):
    """
    从Excel文件导入邮件接收用户数据到EmailRecipient表
    """
    EmailRecipient = apps.get_model("user", "EmailRecipient")

    # 构建Excel文件路径
    excel_file_path = os.path.join(settings.BASE_DIR, "documents", "内控监控邮件接收用户（20250709）.xlsx")

    if not os.path.exists(excel_file_path):
        print(f"警告：Excel文件不存在: {excel_file_path}")
        return

    try:
        # 先清空现有数据，避免重复插入
        EmailRecipient.objects.all().delete()
        print("已清空EmailRecipient表现有数据")

        # 读取Excel文件，第一行为表头
        df = pd.read_excel(excel_file_path, header=0)

        # 清理数据，去除空值
        df = df.dropna(subset=["部门", "真实姓名"])

        # 批量创建EmailRecipient记录
        email_recipients = []
        for _, row in df.iterrows():
            email_recipients.append(
                EmailRecipient(group_name=str(row["部门"]).strip(), real_name=str(row["真实姓名"]).strip())
            )

        # 批量插入数据 (去掉不支持的ignore_conflicts参数)
        if email_recipients:
            EmailRecipient.objects.bulk_create(email_recipients)
            print(f"成功导入 {len(email_recipients)} 条邮件接收用户记录")
        else:
            print("没有有效的数据可导入")

    except Exception as e:
        print(f"导入数据时发生错误: {str(e)}")
        raise


def reverse_load_email_recipients(apps, schema_editor):
    """
    反向迁移：清空EmailRecipient表
    """
    EmailRecipient = apps.get_model("user", "EmailRecipient")
    EmailRecipient.objects.all().delete()
    print("已清空EmailRecipient表数据")


class Migration(migrations.Migration):

    dependencies = [
        ("user", "0002_emailrecipient"),
    ]

    operations = [
        migrations.RunPython(load_email_recipients, reverse_load_email_recipients, hints={"target_app": "user"}),
    ]
