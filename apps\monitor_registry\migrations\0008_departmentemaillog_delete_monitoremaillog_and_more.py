# Generated by Django 4.1.13 on 2025-07-09 15:22

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("dictionary", "0007_auto_20250708_1631"),
        ("monitor_registry", "0007_alter_monitoremaillog_email_type"),
    ]

    operations = [
        migrations.CreateModel(
            name="DepartmentEmailLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("revision", models.IntegerField(default=1, verbose_name="版本号")),
                ("created_by", models.IntegerField(null=True, verbose_name="创建人")),
                (
                    "created_time",
                    models.DateTimeField(
                        auto_now_add=True, null=True, verbose_name="创建时间"
                    ),
                ),
                ("updated_by", models.IntegerField(null=True, verbose_name="更新人")),
                (
                    "updated_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "email_type",
                    models.CharField(
                        choices=[
                            ("ALERT", "提醒邮件"),
                            ("OVERDUE", "逾期提醒邮件"),
                            ("ADMIN_NOTIFICATION", "管理员通知邮件"),
                        ],
                        max_length=20,
                        verbose_name="邮件类型",
                    ),
                ),
                (
                    "recipient_email",
                    models.EmailField(max_length=254, verbose_name="收件人邮箱"),
                ),
                (
                    "email_subject",
                    models.CharField(max_length=500, verbose_name="邮件主题"),
                ),
                ("email_content", models.TextField(verbose_name="邮件内容")),
                (
                    "send_status",
                    models.CharField(
                        choices=[
                            ("PENDING", "待发送"),
                            ("SENDING", "发送中"),
                            ("SUCCESS", "发送成功"),
                            ("FAILED", "发送失败"),
                        ],
                        default="PENDING",
                        max_length=20,
                        verbose_name="发送状态",
                    ),
                ),
                (
                    "send_count",
                    models.IntegerField(default=0, verbose_name="发送尝试次数"),
                ),
                (
                    "last_error",
                    models.TextField(
                        blank=True, null=True, verbose_name="最后错误信息"
                    ),
                ),
                (
                    "scheduled_send_time",
                    models.DateTimeField(verbose_name="预定发送时间"),
                ),
                (
                    "actual_send_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="实际发送时间"
                    ),
                ),
                (
                    "send_date",
                    models.DateField(
                        help_text="邮件发送的日期，用于防止同一天重复发送",
                        verbose_name="发送日期",
                    ),
                ),
                (
                    "monitor_registries",
                    models.ManyToManyField(
                        help_text="包含需要发送邮件的所有监控项目",
                        related_name="department_email_logs",
                        to="monitor_registry.monitorregistry",
                        verbose_name="监控项目列表",
                    ),
                ),
                (
                    "responsible_department",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="department_email_logs",
                        to="dictionary.dictitem",
                        verbose_name="负责部门",
                    ),
                ),
            ],
            options={
                "verbose_name": "部门邮件发送日志",
                "verbose_name_plural": "部门邮件发送日志",
                "db_table": "t_department_email_log",
            },
        ),
        migrations.DeleteModel(
            name="MonitorEmailLog",
        ),
        migrations.AddIndex(
            model_name="departmentemaillog",
            index=models.Index(
                fields=["send_status", "scheduled_send_time"],
                name="t_departmen_send_st_428a9f_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="departmentemaillog",
            index=models.Index(
                fields=["responsible_department", "email_type", "send_date"],
                name="t_departmen_respons_4392d2_idx",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="departmentemaillog",
            unique_together={("responsible_department", "email_type", "send_date")},
        ),
    ]
