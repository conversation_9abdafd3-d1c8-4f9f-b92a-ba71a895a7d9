#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : monitor_registry_view_set.py
<AUTHOR> JT_DA
@Date     : 2025/04/18
@File_Desc: 监控列表视图集
"""

from rest_framework import viewsets
from django.db import transaction
from rest_framework.filters import SearchFilter
from django_filters.rest_framework import DjangoFilterBackend
import os

from apps.monitor_registry.models import MonitorRegistry
from apps.monitor_registry.serializers import MonitorRegistrySerializer
from utils.pagination import MyPageNumberPagination
from utils.ajax_result import AjaxResult
from utils.time_helper import DateTimeUtils
from utils.file_helper import FileDownloadHandler
from internal_control.settings import BASE_DIR, EXPORT_DIR
from utils.permission_helper import permission_required


class BaseMonitorRegistryViewSet(viewsets.ModelViewSet):
    queryset = MonitorRegistry.objects.all().order_by("-updated_time")
    serializer_class = MonitorRegistrySerializer
    pagination_class = MyPageNumberPagination
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_fields = ["monitor_switch", "responsible_department", "monitor_level"]
    search_fields = ["monitor_project", "monitor_point", "monitor_method"]


class MonitorRegistryViewSet(BaseMonitorRegistryViewSet):
    @permission_required(per="内控风险监控清单.监控清单_查询")
    def list(self, request, *args, **kwargs):
        """
        监控列表
        """
        return super().list(request, *args, **kwargs)

    @permission_required(per="内控风险监控清单.监控清单_新增")
    def create(self, request, *args, **kwargs):
        """
        监控列表新增
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        with transaction.atomic():
            instance = serializer.save()
            files = request.FILES.getlist("file", [])
            for file in files:
                instance.attachment.create(file=file, file_name=file.name)

        return AjaxResult.success(msg="新增成功")

    @permission_required(per="内控风险监控清单.监控清单_查询")
    def retrieve(self, request, *args, **kwargs):
        """
        监控列表详情
        """
        response = super().retrieve(request, *args, **kwargs)
        return AjaxResult.success(data=response.data)

    @permission_required(per="内控风险监控清单.监控清单_修改")
    def update(self, request, *args, **kwargs):
        """
        监控列表编辑
        """
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        with transaction.atomic():
            instance = serializer.save()
            file_ids = request.data.getlist("file_id", [])
            files = request.FILES.getlist("file", [])
            instance.attachment.set(file_ids)
            for file in files:
                instance.attachment.create(file=file, file_name=file.name)

        return AjaxResult.success(msg="编辑成功")

    @permission_required(per="内控风险监控清单.监控清单_删除")
    def destroy(self, request, *args, **kwargs):
        """
        监控列表删除
        """
        instance = self.get_object()
        instance.delete()
        return AjaxResult.success(msg="删除成功")


class MonitorRegistryExport(BaseMonitorRegistryViewSet):
    pagination_class = None

    @permission_required(per="内控风险监控清单.监控清单_查询")
    def list(self, request, *args, **kwargs):
        """
        导出监控清单台账
        """
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)

        filename = DateTimeUtils.get_current_datetime_str() + "-" + "内控风险监控清单台账.xlsx"
        source_path = os.path.join(BASE_DIR, EXPORT_DIR, "内控风险监控清单导出模板.xlsx")

        from documentor import monitor_registry_export

        target = monitor_registry_export.main(serializer.data, source_path)
        return FileDownloadHandler.download_single_file(target, filename)
