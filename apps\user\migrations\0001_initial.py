# Generated by Django 4.1.13 on 2025-04-18 16:10

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="SystemGroup",
            fields=[
                (
                    "id",
                    models.IntegerField(
                        primary_key=True, serialize=False, verbose_name="部门id"
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="部门名称"
                    ),
                ),
            ],
            options={
                "verbose_name": "用户部门表",
                "db_table": "t_sys_auth_group",
            },
        ),
        migrations.CreateModel(
            name="SystemUser",
            fields=[
                ("id", models.IntegerField(verbose_name="用户id")),
                (
                    "username",
                    models.CharField(
                        max_length=255,
                        primary_key=True,
                        serialize=False,
                        verbose_name="唯一标识",
                    ),
                ),
                (
                    "real_name",
                    models.Char<PERSON><PERSON>(
                        blank=True, max_length=255, null=True, verbose_name="真实姓名"
                    ),
                ),
                ("is_active", models.<PERSON><PERSON><PERSON><PERSON>ield(null=True, verbose_name="")),
                ("is_staff", models.<PERSON><PERSON>an<PERSON>ield(null=True, verbose_name="")),
                ("is_superuser", models.BooleanField(null=True, verbose_name="")),
                (
                    "all_path_name",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="H4A全路径"
                    ),
                ),
                (
                    "mobile",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="电话"
                    ),
                ),
                (
                    "email",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="邮件地址"
                    ),
                ),
                (
                    "cardid",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="身份证号码"
                    ),
                ),
                (
                    "app_id",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="应用编号"
                    ),
                ),
                ("group_id", models.IntegerField(null=True, verbose_name="部门id")),
                (
                    "group_name",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="部门名称"
                    ),
                ),
                ("role_id", models.IntegerField(null=True, verbose_name="角色id")),
                (
                    "role_name",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="角色名称"
                    ),
                ),
            ],
            options={
                "verbose_name": "用户信息表",
                "db_table": "t_sys_auth_user",
            },
        ),
    ]
