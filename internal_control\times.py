#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@File     ：times.py
<AUTHOR>
@Date     ：2025/4/18
@File_Desc：
"""
import os
from datetime import datetime

from django.conf import settings
from django.core.management import call_command

from internal_control.celery import app


@app.task()
def auto_backup_database():
    # 获取当前日期和时间
    now = datetime.now()

    # 设置备份文件名，例如 backup_2023-07-28_00-00-00.json
    backup_file_name = f"backup_{now.strftime('%Y-%m-%d_%H-%M-%S')}.json"

    # 设置备份保存路径
    backup_path = settings.BACKUP_DIR

    # 构造备份文件的绝对路径
    backup_file_path = os.path.join(backup_path, backup_file_name)

    # 调用 dumpdata 命令导出数据库数据到备份文件
    # with open(backup_file_path, 'w') as backup_file:
    #     call_command('dumpdata', stdout=backup_file)
    backup_file = open(backup_file_path, "w")
    call_command("dumpdata", stdout=backup_file)
    # 防止可能存在的文件流截断
    backup_file.flush()
    backup_file.close()

    print("数据库备份成功！")
