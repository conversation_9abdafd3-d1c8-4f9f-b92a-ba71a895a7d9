#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : monitor_registry_import.py
<AUTHOR> JT_DA
@Date     : 2025/04/24
@File_Desc:
"""

import os
from io import BytesIO

import openpyxl
import pandas as pd
from django.db import transaction
from rest_framework.generics import GenericAP<PERSON>View
from rest_framework.views import APIView

from apps.dictionary.models import DictItem
from apps.monitor_registry import tasks
from apps.monitor_registry.serializers import MonitorRegistrySerializer, MonitorRegistryDevelopmentSerializer
from internal_control.settings import BASE_DIR, IMPORT_DIR
from utils.ajax_result import AjaxResult
from utils.file_helper import FileDownloadHandler
from utils.permission_helper import permission_required


class MonitorRegistryImportTemplate(APIView):
    @permission_required(per="内控风险监控清单.监控清单_新增")
    def get(self, request):
        """
        获取监控清单导入模板
        """
        filename = "内控风险监控清单导入模板.xlsx"
        filepath = os.path.join(BASE_DIR, IMPORT_DIR, filename)
        workbook = openpyxl.load_workbook(filepath)

        # Refactored data writing to sheets into a separate method
        self._write_data_to_sheet(workbook["责任主体"], self.get_dict_items("functional_department"), start_row=1)
        self._write_data_to_sheet(workbook["监控层级"], self.get_dict_items("monitor_level"), start_row=1)
        self._write_data_to_sheet(workbook["监控周期"], self.get_dict_items("monitor_cycle"), start_row=1)

        # Save and return the modified workbook
        memory_file = BytesIO()
        workbook.save(memory_file)
        memory_file.seek(0)
        return FileDownloadHandler.download_single_file(memory_file, filename)

    def get_dict_items(self, type_code, **filter_kwargs):
        return (
            DictItem.objects.select_related("dict_type")
            .filter(dict_type__type_code=type_code, **filter_kwargs)
            .values_list("dict_info", flat=True)
        )

    def _write_data_to_sheet(self, sheet, data, start_row):
        for row_num, item in enumerate(data, start=start_row):
            if isinstance(item, dict):  # For dict data from serializer
                for col_num, value in enumerate(item.values(), start=1):
                    sheet.cell(row=row_num, column=col_num).value = value
            else:  # For flat list data from DictItem
                sheet.cell(row=row_num, column=1).value = item


class MonitorRegistryBatchImport(GenericAPIView):
    serializer_class = MonitorRegistrySerializer

    def __init__(self):
        super().__init__()
        self.error_list = []

    @permission_required(per="内控风险监控清单.监控清单_新增")
    def post(self, request):
        """
        内控风险监控清单批量导入
        """
        file = request.data.get("file", None)
        if not file:
            return AjaxResult.fail(msg="缺少必要参数")

        file_data = BytesIO()
        for chunk in file.chunks():
            file_data.write(chunk)
        file_data.seek(0)  # 将文件指针移回文件开头

        # Sheet 1
        df_data_sheet1 = pd.read_excel(file_data, header=1, sheet_name="Sheet1")
        if df_data_sheet1.empty:
            return AjaxResult.fail(msg="请检查Sheet1是否录入内容")

        df_data_sheet1 = df_data_sheet1.fillna("").astype(str).replace({"nan": "", "_x000D_": "\n"}, regex=False)

        # 将字段名称中"*"去掉
        df_data_sheet1.columns = [col.replace("*", "") for col in df_data_sheet1.columns]

        # Sheet 2
        df_data_sheet2 = pd.read_excel(file_data, header=1, sheet_name="Sheet2")
        if not df_data_sheet2.empty:
            df_data_sheet2 = df_data_sheet2.fillna("").astype(str).replace({"nan": "", "_x000D_": "\n"}, regex=False)
            # 将字段名称中"*"去掉
            df_data_sheet2.columns = [col.replace("*", "") for col in df_data_sheet2.columns]

        with transaction.atomic():
            for idx, row in df_data_sheet1.iterrows():
                item = tasks.process_row_data_for_sheet1(row, request)
                if item:
                    self._insert_data(MonitorRegistrySerializer, item)
            if not df_data_sheet2.empty:
                for idx, row in df_data_sheet2.iterrows():
                    item = tasks.process_row_data_for_sheet2(row, request)
                    if item:
                        self._insert_data(MonitorRegistryDevelopmentSerializer, item)

        return AjaxResult.success()

    def _insert_data(self, serializer_class, data):
        serializer = serializer_class(data=data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
