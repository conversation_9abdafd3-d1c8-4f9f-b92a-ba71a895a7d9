# INTERNAL_CONTROL_GDFS_OAUTH
本项目为广东分署的内控风险监控清单系统后台

## 达梦数据库安装
达梦数据库有其固定的依赖dmPython和django_dmPython，放置在documents文件夹下，安装时需要先安装dmPython和django_dmPython

安装dmPython
```bash
cd documents/dm_driver_linux/dmPython
python setup.py install
```

安装django_dmPython
```bash
cd documents/dm_driver_linux/django_dmPython3.0.0/django_dmPython
python setup.py install
```

dmPython 的运行需要使用 dpi 动态库，应该将 dpi 所在目录加入系统的环境变量，操作步骤如下：
```bash
[root@dm8 dmPython]# vi /root/.bash_profile 
# .bash_profile

# Get the aliases and functions
if [ -f ~/.bashrc ]; then
    . ~/.bashrc
fi

# User specific environment and startup programs

export LD_LIBRARY_PATH="$LD_LIBRARY_PATH:/dm/dmdbms/bin"
export DM_HOME="/dm/dmdbms"
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/dm/dmdbms/drivers/dpi/
[root@dm8 dmPython]# source /root/.bash_profile
```

