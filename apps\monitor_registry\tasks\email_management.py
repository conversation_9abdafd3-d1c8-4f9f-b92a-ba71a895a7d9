#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : email_management.py
<AUTHOR> JT_DA
@Date     : 2025/01/16
@File_Desc: 邮件发送和管理相关函数 - 按负责部门合并发送
"""

import logging
from collections import defaultdict
from datetime import datetime

from django.db import transaction, IntegrityError
from django.utils import timezone

from apps.monitor_registry.models import MonitorRegistry, DepartmentEmailLog
from apps.dictionary.models import DictItem
from apps.monitor_registry.tasks.monitor_cycle import (
    check_development_records_in_range,
    is_period_end,
    calculate_previous_period_end_date,
    QUARTER_END_MONTHS,
    HALF_YEAR_END_MONTHS,
    YEAR_END_MONTHS,
)
from apps.user.models import EmailRecipient, SystemUser
from internal_control.constant import (
    ALERT_EMAIL_TEMPLATE,
    OVERDUE_ALERT_EMAIL_TEMPLATE,
    EMAIL_SUBJECT,
    ADMIN_EMAIL_LIST,
    ADMIN_NOTIFICATION_EMAIL_TEMPLATE,
)
from utils.email_helper import send_email_with_settings

# 配置日志
logger = logging.getLogger(__name__)


def format_project_list_for_email(project_names):
    """
    将项目名称列表格式化为邮件内容格式：【项目1】、【项目2】、【项目N】

    :param project_names: 项目名称列表
    :return: 格式化后的字符串
    """
    if not project_names:
        return ""
    return "、".join([f"【{project}】" for project in project_names])


def get_department_business_emails(department_name):
    """
    根据部门名称获取对应业务人员的邮箱地址列表

    :param department_name: 部门名称
    :return: 邮箱地址列表
    """
    try:
        # 根据部门名称从EmailRecipient获取业务人员姓名列表
        email_recipients = EmailRecipient.objects.filter(group_name=department_name)
        real_names = [recipient.real_name for recipient in email_recipients]

        if not real_names:
            logger.warning(f"部门 {department_name} 在EmailRecipient中没有找到对应的业务人员")
            return []

        # 根据部门名称和真实姓名从SystemUser获取邮箱地址
        system_users = SystemUser.objects.filter(
            group_name=department_name, real_name__in=real_names, email__isnull=False, email__gt=""  # 确保邮箱不为空
        )

        emails = [user.email for user in system_users if user.email]

        if not emails:
            logger.warning(f"部门 {department_name} 的业务人员 {real_names} 在SystemUser中没有找到有效的邮箱地址")
            return []

        logger.info(f"部门 {department_name} 获取到 {len(emails)} 个业务人员邮箱: {emails}")
        return emails

    except Exception as e:
        logger.error(f"获取部门 {department_name} 业务人员邮箱失败: {str(e)}")
        return []


def group_monitor_registries_by_business_emails(monitor_registries, email_type):
    """
    按负责部门的业务人员邮箱分组监控项目

    :param monitor_registries: 监控项目列表
    :param email_type: 邮件类型
    :return: 按邮箱分组的字典 {(department, email): [registries]}
    """
    email_groups = defaultdict(list)

    for registry in monitor_registries:
        if not registry.responsible_department:
            logger.warning(f"监控项目 {registry.monitor_project} 没有设置负责部门，跳过")
            continue

        department_name = registry.responsible_department.dict_info
        business_emails = get_department_business_emails(department_name)

        if not business_emails:
            logger.warning(f"负责部门 {department_name} 没有找到对应的业务人员邮箱，跳过")
            continue

        # 为每个业务人员邮箱创建分组
        for email in business_emails:
            email_groups[(registry.responsible_department, email)].append(registry)

    return email_groups


def create_or_get_department_email_log(
    responsible_department, recipient_email, email_type, monitor_registries, subject, content, scheduled_time, send_date
):
    """
    创建或获取部门邮件发送记录，如果已存在则跳过

    :param responsible_department: 负责部门实例
    :param recipient_email: 接收人邮箱地址
    :param email_type: 邮件类型 ('ALERT' 或 'OVERDUE' 或 'ADMIN_NOTIFICATION')
    :param monitor_registries: 监控项目实例列表
    :param subject: 邮件主题
    :param content: 邮件内容
    :param scheduled_time: 预定发送时间
    :param send_date: 发送日期
    :return: (email_log实例, 是否新创建)
    """
    try:
        with transaction.atomic():
            email_log, created = DepartmentEmailLog.objects.get_or_create(
                responsible_department=responsible_department,
                recipient_email=recipient_email,
                email_type=email_type,
                send_date=send_date,
                defaults={
                    "email_subject": subject,
                    "email_content": content,
                    "scheduled_send_time": scheduled_time,
                    "send_status": "PENDING",
                },
            )

            if created:
                # ManyToManyField需要在对象保存后才能设置
                email_log.monitor_registries.set(monitor_registries)
                logger.info(
                    f"创建新的业务人员邮件发送记录: {responsible_department.dict_info} - {recipient_email} - {email_type} "
                    f"({len(monitor_registries)}个项目) - {send_date}"
                )
            else:
                logger.info(
                    f"业务人员邮件发送记录已存在，跳过创建: {responsible_department.dict_info} - {recipient_email} - {email_type} - {send_date}"
                )

            return email_log, created

    except IntegrityError as e:
        logger.warning(f"业务人员邮件记录创建失败，可能存在并发冲突: {e}")
        # 如果并发创建导致约束冲突，尝试获取已存在的记录
        try:
            email_log = DepartmentEmailLog.objects.get(
                responsible_department=responsible_department,
                recipient_email=recipient_email,
                email_type=email_type,
                send_date=send_date,
            )
            return email_log, False
        except DepartmentEmailLog.DoesNotExist:
            logger.error(f"无法获取已存在的业务人员邮件记录，可能存在数据一致性问题")
            raise


def send_department_email_with_logging(email_log):
    """
    安全地发送业务人员邮件并记录发送状态

    :param email_log: DepartmentEmailLog实例
    :return: 发送成功标志
    """
    try:
        # 更新状态为发送中
        email_log.send_status = "SENDING"
        email_log.send_count += 1
        email_log.save(update_fields=["send_status", "send_count"])

        department_name = email_log.responsible_department.dict_info
        recipient_email = email_log.recipient_email
        project_count = email_log.monitor_registries.count()
        logger.info(
            f"开始发送邮件: {department_name} - {recipient_email} - {email_log.email_type} "
            f"({project_count}个项目) - 尝试第{email_log.send_count}次"
        )

        # 使用SMTP直接发送邮件
        success, error_message = send_email_with_settings(
            receiver_email=email_log.recipient_email,
            subject=email_log.email_subject,
            content=email_log.email_content,
        )

        # 记录实际发送时间
        email_log.actual_send_time = timezone.now()

        if success:
            # 发送成功
            email_log.send_status = "SUCCESS"
            email_log.last_error = None
            email_log.save(update_fields=["send_status", "last_error", "actual_send_time"])

            logger.info(
                f"邮件发送成功: {department_name} - {recipient_email} - {email_log.email_type} ({project_count}个项目)"
            )
            return True
        else:
            # 发送失败
            email_log.send_status = "FAILED"
            email_log.last_error = error_message
            email_log.save(update_fields=["send_status", "last_error", "actual_send_time"])

            logger.error(
                f"邮件发送失败: {department_name} - {recipient_email} - {email_log.email_type} ({project_count}个项目), 错误: {error_message}"
            )
            return False

    except Exception as e:
        # 其他异常
        error_msg = f"未知异常: {str(e)}"
        email_log.send_status = "FAILED"
        email_log.last_error = error_msg
        email_log.actual_send_time = timezone.now()
        email_log.save(update_fields=["send_status", "last_error", "actual_send_time"])

        department_name = email_log.responsible_department.dict_info
        recipient_email = email_log.recipient_email
        project_count = email_log.monitor_registries.count()
        logger.error(
            f"邮件发送未知异常: {department_name} - {recipient_email} - {email_log.email_type} ({project_count}个项目), 错误: {error_msg}"
        )
        return False


def schedule_department_alert_emails(monitor_registries, email_type="ALERT"):
    """
    按负责部门的业务人员邮箱分组并发送提醒邮件

    :param monitor_registries: 需要发送邮件的监控项目列表
    :param email_type: 邮件类型 ('ALERT' 或 'OVERDUE')
    :return: 发送成功的数量
    """
    today = datetime.now().date()
    success_count = 0

    # 按负责部门的业务人员邮箱分组
    email_groups = group_monitor_registries_by_business_emails(monitor_registries, email_type)

    for (department, recipient_email), registries in email_groups.items():
        try:
            # 收集项目信息
            project_names = [registry.monitor_project for registry in registries]

            # 格式化项目列表为邮件内容
            formatted_project_list = format_project_list_for_email(project_names)

            # 选择邮件模板并替换RD
            if email_type == "OVERDUE":
                content = OVERDUE_ALERT_EMAIL_TEMPLATE.replace("RD", department.dict_info)
            else:
                content = ALERT_EMAIL_TEMPLATE.replace("RD", department.dict_info)

            # 将MPS替换为格式化的项目列表
            content = content.replace("MPS", formatted_project_list)

            # 创建业务人员邮件记录
            email_log, created = create_or_get_department_email_log(
                responsible_department=department,
                recipient_email=recipient_email,
                email_type=email_type,
                monitor_registries=registries,
                subject=EMAIL_SUBJECT,
                content=content,
                scheduled_time=timezone.now(),
                send_date=today,
            )

            if created:
                # 立即尝试发送
                success = send_department_email_with_logging(email_log)
                if success:
                    success_count += 1
            else:
                logger.info(
                    f"业务人员邮件已发送过，跳过重复发送: {department.dict_info} - {recipient_email} - {email_type}"
                )
                success_count += 1

        except Exception as e:
            logger.error(
                f"业务人员邮件安排发送失败: {department.dict_info} - {recipient_email} - {email_type}, 错误: {str(e)}"
            )

    return success_count


def collect_overdue_monitor_projects():
    """
    收集所有逾期未更新的监控项目，按部门分组

    :return: 逾期项目列表，每个元素包含监控项目和负责部门信息
    """
    today = datetime.now().date()
    current_day = today.day
    current_month = today.month

    overdue_projects = []

    # 只在11日执行逾期检查
    if current_day != 11:
        return overdue_projects

    # 获取所有启用的监控记录
    monitor_registries = MonitorRegistry.objects.filter(
        monitor_switch=True, monitor_cycle__isnull=False
    ).select_related("monitor_cycle", "responsible_department")

    for monitor_registry in monitor_registries:
        monitor_cycle_name = monitor_registry.monitor_cycle.dict_info if monitor_registry.monitor_cycle else ""
        is_overdue = False

        if monitor_cycle_name == "每月监控":
            # 每月监控：11日检查是否存在1-10日的开展情况记录
            start_date = datetime(today.year, today.month, 1)
            end_date = datetime(today.year, today.month, 10, 23, 59, 59)
            if not check_development_records_in_range(monitor_registry, start_date, end_date):
                is_overdue = True

        elif monitor_cycle_name == "季度监控" and current_month in [1, 4, 7, 10]:
            # 季度监控：季度第11日检查当前月份1-10日的开展情况
            start_date = datetime(today.year, today.month, 1)
            end_date = datetime(today.year, today.month, 10, 23, 59, 59)
            if not check_development_records_in_range(monitor_registry, start_date, end_date):
                is_overdue = True

        elif monitor_cycle_name == "半年监控" and current_month in [1, 7]:
            # 半年监控：半年第11日检查当前月份1-10日的开展情况
            start_date = datetime(today.year, today.month, 1)
            end_date = datetime(today.year, today.month, 10, 23, 59, 59)
            if not check_development_records_in_range(monitor_registry, start_date, end_date):
                is_overdue = True

        elif monitor_cycle_name == "年度监控" and current_month == 1:
            # 年度监控：次年1月11日检查当前月份1-10日的开展情况
            start_date = datetime(today.year, today.month, 1)
            end_date = datetime(today.year, today.month, 10, 23, 59, 59)
            if not check_development_records_in_range(monitor_registry, start_date, end_date):
                is_overdue = True

        if is_overdue:
            department_name = "未设置部门"
            if monitor_registry.responsible_department:
                department_name = monitor_registry.responsible_department.dict_info

            overdue_projects.append(
                {
                    "monitor_project": monitor_registry.monitor_project,
                    "responsible_department": department_name,
                    "monitor_registry": monitor_registry,
                }
            )

    return overdue_projects


def format_admin_notification_content(overdue_projects):
    """
    格式化管理员通知邮件内容

    :param overdue_projects: 逾期项目列表
    :return: 格式化的邮件内容
    """
    if not overdue_projects:
        return "本期所有监控项目均已及时更新，无逾期项目。"

    project_count = len(overdue_projects)

    project_lines = []
    for i, project in enumerate(overdue_projects, 1):
        line = f"{i}、{project['responsible_department']} - {project['monitor_project']}"
        project_lines.append(line)

    project_list = "\n".join(project_lines)

    # 使用模板格式化邮件内容
    return ADMIN_NOTIFICATION_EMAIL_TEMPLATE.format(project_count=project_count, project_list=project_list)


def send_admin_notification_email(overdue_projects):
    """
    发送管理员通知邮件

    :param overdue_projects: 逾期项目列表
    :return: 发送成功的数量
    """
    if not overdue_projects:
        logger.info("没有逾期项目，无需发送管理员通知邮件")
        return 0

    if not ADMIN_EMAIL_LIST:
        logger.warning("管理员邮箱列表为空，跳过管理员通知邮件发送")
        return 0

    # 格式化邮件内容
    email_content = format_admin_notification_content(overdue_projects)

    # 生成周期标识符用于防重复发送
    today = datetime.now().date()

    success_count = 0

    # 获取督查审计工作处的部门记录
    try:
        audit_department = DictItem.objects.get(dict_code="督查审计工作处")
    except DictItem.DoesNotExist:
        logger.error("未找到dict_code为'督查审计工作处'的部门记录，无法发送管理员通知邮件")
        return 0
    except Exception as e:
        logger.error(f"查询督查审计工作处部门记录失败: {str(e)}")
        return 0

    for admin_email in ADMIN_EMAIL_LIST:
        try:
            # 生成动态邮件主题
            today_str = today.strftime("%Y-%m-%d")
            dynamic_subject = f"{today_str}内控风险监控逾期未更新情况通知"

            # 为管理员通知创建特殊的邮件记录（使用督查审计工作处作为负责部门）
            try:
                with transaction.atomic():
                    email_log, created = DepartmentEmailLog.objects.get_or_create(
                        responsible_department=audit_department,
                        recipient_email=admin_email,
                        email_type="ADMIN_NOTIFICATION",
                        send_date=today,
                        defaults={
                            "email_subject": dynamic_subject,
                            "email_content": email_content,
                            "scheduled_send_time": timezone.now(),
                            "send_status": "PENDING",
                        },
                    )

                    if created:
                        # 管理员通知邮件不需要关联具体的监控项目
                        # 立即尝试发送
                        success = send_department_email_with_logging(email_log)
                        if success:
                            success_count += 1
                            logger.info(f"管理员通知邮件发送成功: {admin_email}")
                        else:
                            logger.error(f"管理员通知邮件发送失败: {admin_email}")
                    else:
                        logger.info(f"管理员通知邮件已发送过，跳过: {admin_email}")
                        success_count += 1

            except Exception as e:
                logger.error(f"创建管理员通知邮件记录失败: {admin_email}, 错误: {str(e)}")

        except Exception as e:
            logger.error(f"发送管理员通知邮件异常: {admin_email}, 错误: {str(e)}")

    logger.info(f"管理员通知邮件发送完成，成功: {success_count}/{len(ADMIN_EMAIL_LIST)}")
    return success_count


def _calculate_start_date_by_cycle(today, monitor_cycle_name):
    """
    根据监控周期类型计算相应的start_date

    :param today: 当前日期
    :param monitor_cycle_name: 监控周期名称
    :return: 计算后的start_date
    """
    # 计算上一个月的年份和月份
    if today.month == 1:
        previous_year = today.year - 1
        previous_month = 12
    else:
        previous_year = today.year
        previous_month = today.month - 1

    # 根据监控周期类型确定日期
    if monitor_cycle_name == "每月监控":
        # 每月监控：上一个月的20日
        start_day = 20
    else:
        # 其他监控周期：上一个月的1日
        start_day = 1

    return datetime(previous_year, previous_month, start_day)


def _check_overdue_for_cycle(monitor_registry, monitor_cycle_name, current_month, start_date, end_date):
    """
    检查指定监控周期是否逾期的通用逻辑

    :param monitor_registry: 监控项目
    :param monitor_cycle_name: 监控周期名称
    :param current_month: 当前月份
    :param start_date: 检查开始日期
    :param end_date: 检查结束日期
    :return: 是否逾期
    """
    if monitor_cycle_name == "每月监控":
        return not check_development_records_in_range(monitor_registry, start_date, end_date)
    elif monitor_cycle_name == "季度监控" and current_month in [1, 4, 7, 10]:
        return not check_development_records_in_range(monitor_registry, start_date, end_date)
    elif monitor_cycle_name == "半年监控" and current_month in [1, 7]:
        return not check_development_records_in_range(monitor_registry, start_date, end_date)
    elif monitor_cycle_name == "年度监控" and current_month == 1:
        return not check_development_records_in_range(monitor_registry, start_date, end_date)
    return False


def collect_all_projects_by_date(monitor_registries, today):
    """
    统一收集需要发送邮件的监控项目，避免重复逻辑

    :param monitor_registries: 监控项目列表
    :param today: 当前日期
    :return: {
        'alert_projects': [...],
        'overdue_projects': [...],
        'admin_overdue_projects': [...]
    }
    """
    current_day = today.day
    current_month = today.month

    alert_projects = []
    overdue_projects = []
    admin_overdue_projects = []

    # 预计算end_date（仅在11日需要）
    end_date = None
    if current_day == 11:
        end_date = datetime(today.year, today.month, 10, 23, 59, 59)

    for monitor_registry in monitor_registries:
        monitor_cycle_name = monitor_registry.monitor_cycle.dict_info if monitor_registry.monitor_cycle else ""

        # 期末提醒邮件逻辑
        should_send_alert = False
        if monitor_cycle_name == "每月监控" and is_period_end(today):
            should_send_alert = True
        elif monitor_cycle_name == "季度监控" and is_period_end(today, QUARTER_END_MONTHS):
            should_send_alert = True
        elif monitor_cycle_name == "半年监控" and is_period_end(today, HALF_YEAR_END_MONTHS):
            should_send_alert = True
        elif monitor_cycle_name == "年度监控" and is_period_end(today, YEAR_END_MONTHS):
            should_send_alert = True

        if should_send_alert:
            alert_projects.append(monitor_registry)

        # 逾期提醒邮件逻辑（11日检查）
        if current_day == 11 and end_date:
            # 根据监控周期类型动态计算start_date
            start_date = _calculate_start_date_by_cycle(today, monitor_cycle_name)
            is_overdue = _check_overdue_for_cycle(monitor_registry, monitor_cycle_name, current_month, start_date, end_date)

            if is_overdue:
                overdue_projects.append(monitor_registry)
                # 同时收集管理员通知需要的数据
                department_name = "未设置部门"
                if monitor_registry.responsible_department:
                    department_name = monitor_registry.responsible_department.dict_info

                admin_overdue_projects.append({
                    "monitor_project": monitor_registry.monitor_project,
                    "responsible_department": department_name,
                    "monitor_registry": monitor_registry,
                })

    return {
        'alert_projects': alert_projects,
        'overdue_projects': overdue_projects,
        'admin_overdue_projects': admin_overdue_projects
    }


def collect_projects_by_criteria(monitor_registries, today, email_type):
    """
    根据标准收集需要发送邮件的监控项目（保持向后兼容）

    :param monitor_registries: 监控项目列表
    :param today: 当前日期
    :param email_type: 邮件类型 ('ALERT' 或 'OVERDUE')
    :return: 需要发送邮件的项目列表
    """
    projects_data = collect_all_projects_by_date(monitor_registries, today)

    if email_type == "ALERT":
        return projects_data['alert_projects']
    elif email_type == "OVERDUE":
        return projects_data['overdue_projects']
    else:
        return []


def check_and_resend_missed_period_end_alerts(monitor_registries, today):
    """
    检查是否存在漏发的上一期期末提醒邮件，如有则补发
    使用新的按部门分组发送逻辑

    :param monitor_registries: 启用的监控项目列表
    :param today: 当前日期
    :return: 补发成功的数量
    """
    # 按部门和周期分组收集需要补发的项目
    resend_projects = []

    for monitor_registry in monitor_registries:
        monitor_cycle_name = monitor_registry.monitor_cycle.dict_info if monitor_registry.monitor_cycle else ""

        # 计算上一期期末日期
        previous_period_end = calculate_previous_period_end_date(today, monitor_cycle_name)

        # 检查是否为不支持的监控周期类型
        if previous_period_end is None:
            logger.info(f"跳过不支持的监控周期类型: {monitor_cycle_name} - {monitor_registry.monitor_project}")
            continue

        # 若上一期期末日期为"2025-06-30"，则跳过
        if previous_period_end.strftime("%Y-%m-%d") == "2025-06-30":
            logger.info(f"跳过2025-06-30期末提醒邮件补发检查: {monitor_registry.monitor_project}")
            continue

        # 检查是否存在成功发送的期末提醒邮件记录（检查所有业务人员邮箱）
        if not monitor_registry.responsible_department:
            continue

        department_name = monitor_registry.responsible_department.dict_info
        business_emails = get_department_business_emails(department_name)

        if not business_emails:
            logger.warning(f"部门 {department_name} 没有找到业务人员邮箱，跳过补发检查")
            continue

        # 检查所有业务人员邮箱是否都已成功发送
        all_sent = True
        for email in business_emails:
            existing_alert = DepartmentEmailLog.objects.filter(
                responsible_department=monitor_registry.responsible_department,
                recipient_email=email,
                email_type="ALERT",
                send_status="SUCCESS",
                send_date=previous_period_end,
            ).exists()

            if not existing_alert:
                all_sent = False
                break

        if not all_sent:
            logger.info(
                f"检测到漏发的期末提醒邮件: {monitor_registry.monitor_project} - {previous_period_end.strftime('%Y-%m-%d')}"
            )
            resend_projects.append(monitor_registry)

    # 按部门分组发送补发邮件
    if resend_projects:
        resend_count = schedule_department_alert_emails(resend_projects, email_type="ALERT")
        logger.info(f"成功补发期末提醒邮件: {resend_count}个部门")
        return resend_count
    else:
        logger.info("无需补发期末提醒邮件")
        return 0


def check_and_resend_missed_overdue_alerts(monitor_registries, today):
    """
    检查是否存在漏发的11日逾期提醒邮件，如有则补发
    使用新的按部门分组发送逻辑

    :param monitor_registries: 启用的监控项目列表
    :param today: 当前日期
    :return: 补发成功的数量
    """
    current_month = today.month
    resend_projects = []

    # 计算11日的日期
    day_11_date = datetime(today.year, today.month, 11).date()

    for monitor_registry in monitor_registries:
        monitor_cycle_name = monitor_registry.monitor_cycle.dict_info if monitor_registry.monitor_cycle else ""

        # 根据监控周期判断是否应该在11日发送逾期提醒
        should_send_overdue = False

        if monitor_cycle_name == "每月监控":
            should_send_overdue = True
        elif monitor_cycle_name == "季度监控" and current_month in [1, 4, 7, 10]:
            should_send_overdue = True
        elif monitor_cycle_name == "半年监控" and current_month in [1, 7]:
            should_send_overdue = True
        elif monitor_cycle_name == "年度监控" and current_month == 1:
            should_send_overdue = True

        if should_send_overdue:
            if not monitor_registry.responsible_department:
                continue

            department_name = monitor_registry.responsible_department.dict_info
            business_emails = get_department_business_emails(department_name)

            if not business_emails:
                logger.warning(f"部门 {department_name} 没有找到业务人员邮箱，跳过逾期补发检查")
                continue

            # 检查所有业务人员邮箱是否都已成功发送逾期提醒邮件
            all_sent = True
            for email in business_emails:
                existing_overdue = DepartmentEmailLog.objects.filter(
                    responsible_department=monitor_registry.responsible_department,
                    recipient_email=email,
                    email_type="OVERDUE",
                    send_status="SUCCESS",
                    send_date=day_11_date,
                ).exists()

                if not existing_overdue:
                    all_sent = False
                    break

            if not all_sent:
                # 检查1-10日是否存在开展情况记录
                start_date = datetime(today.year, today.month, 1)
                end_date = datetime(today.year, today.month, 10, 23, 59, 59)

                if not check_development_records_in_range(monitor_registry, start_date, end_date):
                    logger.info(f"检测到漏发的逾期提醒邮件: {monitor_registry.monitor_project}")
                    resend_projects.append(monitor_registry)

    # 按部门分组发送补发邮件
    if resend_projects:
        resend_count = schedule_department_alert_emails(resend_projects, email_type="OVERDUE")
        logger.info(f"成功补发逾期提醒邮件: {resend_count}个部门")
        return resend_count
    else:
        logger.info("无需补发逾期提醒邮件")
        return 0
