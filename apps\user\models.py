from django.db import models


class SystemUser(models.Model):
    id = models.IntegerField(verbose_name="用户id")
    username = models.CharField(max_length=255, verbose_name="唯一标识", primary_key=True)
    real_name = models.CharField(max_length=255, null=True, blank=True, verbose_name="真实姓名")
    is_active = models.BooleanField(null=True, verbose_name="")
    is_staff = models.BooleanField(null=True, verbose_name="")
    is_superuser = models.BooleanField(null=True, verbose_name="")
    all_path_name = models.CharField(max_length=255, null=True, blank=True, verbose_name="H4A全路径")
    mobile = models.CharField(max_length=50, null=True, blank=True, verbose_name="电话")
    email = models.Char<PERSON>ield(max_length=50, null=True, blank=True, verbose_name="邮件地址")
    cardid = models.Cha<PERSON><PERSON><PERSON>(max_length=50, null=True, blank=True, verbose_name="身份证号码")
    app_id = models.CharField(max_length=50, null=True, blank=True, verbose_name="应用编号")
    group_id = models.IntegerField(null=True, verbose_name="部门id")
    group_name = models.CharField(max_length=255, null=True, blank=True, verbose_name="部门名称")
    role_id = models.IntegerField(null=True, verbose_name="角色id")
    role_name = models.CharField(max_length=255, null=True, blank=True, verbose_name="角色名称")

    class Meta:
        db_table = "t_sys_auth_user"
        verbose_name = "用户信息表"

    def __str__(self):
        return self.username


class SystemGroup(models.Model):
    id = models.IntegerField(verbose_name="部门id", primary_key=True)
    name = models.CharField(max_length=255, null=True, blank=True, verbose_name="部门名称")

    class Meta:
        db_table = "t_sys_auth_group"
        verbose_name = "用户部门表"

    def __str__(self):
        return self.name


class EmailRecipient(models.Model):
    """邮件接收用户模型"""
    id = models.AutoField(primary_key=True, verbose_name="id")
    group_name = models.CharField(max_length=255, verbose_name="部门名称")
    real_name = models.CharField(max_length=255, verbose_name="真实姓名")

    class Meta:
        db_table = "t_email_recipient"
        verbose_name = "邮件接收用户"

    def __str__(self):
        return f"{self.group_name} - {self.real_name}"
