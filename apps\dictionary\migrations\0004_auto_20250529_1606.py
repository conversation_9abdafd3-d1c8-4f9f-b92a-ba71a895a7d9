# Generated by Django 4.1.13 on 2025-05-29 16:06

import os
import pandas as pd
from django.db import migrations, transaction
from django.conf import settings


def import_dict_data(apps, schema_editor):
    """
    从Excel文件导入字典数据到数据库

    参数:
        apps: Django应用注册表，用于获取模型
        schema_editor: 数据库schema编辑器
    """
    DictType = apps.get_model("dictionary", "DictType")
    DictItem = apps.get_model("dictionary", "DictItem")

    # 获取文件路径
    excel_path = os.path.join(settings.BASE_DIR, "documents", "内控监控系统字典（20250529_2）.xlsx")

    try:
        # 检查文件是否存在
        if not os.path.exists(excel_path):
            return

        data = pd.read_excel(excel_path)

        # 验证数据结构
        required_columns = ["类型编码", "类型名称", "字典编码", "字典信息"]
        for col in required_columns:
            if col not in data.columns:
                return

        # 使用事务进行批量导入
        with transaction.atomic():
            # 处理字典类型
            dict_types = {}
            dict_items = {}
            parent_relations = []

            # 第一次遍历: 创建字典类型和字典项
            for _, row in data.iterrows():
                type_code = row["类型编码"]
                type_name = row["类型名称"]
                dict_code = row["字典编码"]
                dict_info = row["字典信息"]

                # 如果字典类型不存在，则创建
                if type_code not in dict_types:
                    dict_type, _ = DictType.objects.get_or_create(type_code=type_code, type_name=type_name)
                    dict_types[type_code] = dict_type
                else:
                    dict_type = dict_types[type_code]

                # 创建字典项
                dict_item = DictItem.objects.create(dict_type=dict_type, dict_code=dict_code, dict_info=dict_info)

                # 存储字典项以便后续处理父子关系
                item_key = f"{type_code}_{dict_code}"
                dict_items[item_key] = dict_item

                # 记录父子关系
                parent = row["父级"]
                if pd.notna(parent) and parent:
                    parent_key = f"{type_code}_{parent}"
                    parent_relations.append((dict_item, parent_key))

            # 第二次遍历: 处理父子关系
            for dict_item, parent_key in parent_relations:
                if parent_key in dict_items:
                    parent_item = dict_items[parent_key]
                    dict_item.parent = parent_item
                    dict_item.save()

    except Exception:
        # 如果出现错误，继续传播异常
        raise


class Migration(migrations.Migration):

    dependencies = [
        ("dictionary", "0003_auto_20250529_1007"),
    ]

    operations = [
        migrations.RunPython(import_dict_data, reverse_code=migrations.RunPython.noop),
    ]
