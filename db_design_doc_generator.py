#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File: db_design_doc_generator.py
@Author: JT_DEV
@LastCommit: 
"""

import os

import django
from django.apps import apps
from docx import Document

# 创建映射字典
type_mapping = {
    "AutoField": "INT",
    "BigAutoField": "BIGINT",
    "BigIntegerField": "BIGINT",
    "BinaryField": "BLOB",
    "BooleanField": "BOOL",
    "CharField": "VARCHAR",
    "DateField": "DATE",
    "DateTimeField": "DATETIME",
    "DecimalField": "DECIMAL",
    "DurationField": "TIME",
    "FileField": "VARCHAR",
    "FilePathField": "VARCHAR",
    "FloatField": "FLOAT",
    "IntegerField": "INT",
    "GenericIPAddressField": "VARCHAR",
    "ImageField": "VARCHAR",
    "J<PERSON><PERSON>ield": "JSON",
    "NullB<PERSON>eanField": "BOOL",
    "PositiveBigIntegerField": "BIGINT",
    "PositiveIntegerField": "INT",
    "PositiveSmallIntegerField": "SMALLINT",
    "SlugField": "VARCHAR",
    "SmallAutoField": "SMALLINT",
    "SmallIntegerField": "SMALLINT",
    "TextField": "TEXT",
    "TimeField": "TIME",
    "URLField": "VARCHAR",
    "UUIDField": "VARCHAR",
    "ForeignKey": "INT",
    "OneToOneField": "INT",
    "ManyToManyField": "INT",
    "ManyToManyRel": "INT",
    "ManyToOneRel": "INT",
    "OneToOneRel": "INT",
    "GenericRel": "INT",
    "ArrayField": "ARRAY",
    "BigIntegerRangeField": "BIGINT",
    "CICharField": "VARCHAR",
    "CIEmailField": "VARCHAR",
    "CITextField": "TEXT",
    "EmailField": "VARCHAR",
    "IPAddressField": "VARCHAR",
    "PhoneField": "VARCHAR",
}

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "jijian_jiancha.settings")
django.setup()

doc = Document()

doc.add_heading("数据库设计文档", level=1)


all_table_name = []

# 排除 Django 自带的模型
excluded_apps = [
    "auth",
    "contenttypes",
    "sessions",
    "admin",
    "sites",
    "messages",
    "staticfiles",
    "sessions",
    "admin",
    "django.contrib",
    "django_celery_results",
]

for model in apps.get_models():
    # 如果模型的 app_label 在排除列表中，跳过该模型
    if model._meta.app_label in excluded_apps:
        continue
    doc.add_heading(model._meta.object_name, level=2)
    doc.add_paragraph(f"{model._meta.db_table} [{model._meta.verbose_name}]").bold = (
        True
    )
    all_table_name.append(f"{model._meta.db_table} [{model._meta.verbose_name}]")

    table = doc.add_table(rows=1, cols=5)
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = "字段名称"
    hdr_cells[1].text = "字段类型"
    hdr_cells[2].text = "是否为空"
    hdr_cells[3].text = "是否主键"
    hdr_cells[4].text = "详细名称"

    for field in model._meta.fields:
        row_cells = table.add_row().cells
        row_cells[0].text = field.name
        row_cells[1].text = type_mapping.get(
            field.get_internal_type(), field.get_internal_type()
        )
        row_cells[2].text = "√" if field.null else ""
        row_cells[3].text = "√" if field.primary_key else ""
        row_cells[4].text = field.verbose_name

    doc.add_paragraph()

doc.save("广东分署-纪检监察数据库设计文档.docx")
print("数据库设计文档生成成功！")
