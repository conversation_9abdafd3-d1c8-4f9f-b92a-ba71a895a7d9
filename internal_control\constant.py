#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : constant.py
<AUTHOR> JT_DA
@Date     : 2025/04/15
@File_Desc:
"""
import enum

from utils import TupleConstant


class RedisKey(enum.Enum):
    DASHBOARD_ARCHIVE = "dashboard_archive"
    ISSUE_FOR_DASHBOARD = "issue_for_dashboard"
    AUTH_USER_INFO = "auth_user_info"
    AUTH_TOKEN_AND_USER_INFO = "auth_token_and_user_info"
    VERIFIED_TOKEN = "verified_token"
    DICTIONARY = "dictionary"


EMAIL_SUBJECT = "您有一条分署内控风险监控的待办信息"

ALERT_EMAIL_TEMPLATE = """
RD，贵部门本期内控风险监控开展情况待更新。请内控风险监控联络员点击进入“广东分署政务系统大监督应用功能模块”（http://10.96.74.149:8001/internal_control/），及时反馈MPS经部门负责人审核同意的内控风险监控结果。具体操作如下:
	1、点击右方“操作”栏下方“编辑开展”菜单可在系统上反馈上一期监控结果。（必填项，每期均需填写）
	2、点击右方“操作”栏下方“编辑”菜单里面选择“上传附件”可在系统上反馈监控结果相关的佐证材料。（选填项，如有需要则上传相关附件）
	3、点击右方“操作”栏下方“编辑”菜单可在系统上更新维护当前监控项目。（选填项，如有需要则更新维护）
	4、点击右上方“新增”栏可编辑增加新的监控项目。（选填项，如有需要则新增维护）

	使用中如有问题和建议请联系督审处权勇（8439）。
"""

OVERDUE_ALERT_EMAIL_TEMPLATE = """
RD，贵部门本期内控风险监控开展情况未更新。请内控风险监控联络员点击进入“广东分署政务系统大监督应用功能模块”（http://10.96.74.149:8001/internal_control/），及时反馈MPS经部门负责人审核同意的内控风险监控结果。具体操作如下:
	1、点击右方“操作”栏下方“编辑开展”菜单可在系统上反馈上一期监控结果。（必填项，每期均需填写）
	2、点击右方“操作”栏下方“编辑”菜单里面选择“上传附件”可在系统上反馈监控结果相关的佐证材料。（选填项，如有需要则上传相关附件）
	3、点击右方“操作”栏下方“编辑”菜单可在系统上更新维护当前监控项目。（选填项，如有需要则更新维护）
	4、点击右上方“新增”栏可编辑增加新的监控项目。（选填项，如有需要则新增维护）

	使用中如有问题和建议请联系督审处权勇（8439）。
"""

# 管理员通知邮件模板
ADMIN_NOTIFICATION_EMAIL_TEMPLATE = """
本期尚有{project_count}个监控项目未更新，具体如下：
{project_list}

请予以关注。
"""

# 邮件发送相关常量


# 管理员邮箱地址列表
ADMIN_EMAIL_LIST = [
    "<EMAIL>",  # 张涛
    "<EMAIL>",  # 朱丹
    "<EMAIL>",  # 权勇
]
