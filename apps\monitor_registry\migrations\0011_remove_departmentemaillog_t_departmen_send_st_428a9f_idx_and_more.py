# Generated by Django 4.1.13 on 2025-07-11 16:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("dictionary", "0007_auto_20250708_1631"),
        ("monitor_registry", "0010_alter_departmentemaillog_updated_time_and_more"),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name="departmentemaillog",
            name="t_departmen_send_st_428a9f_idx",
        ),
        migrations.RemoveIndex(
            model_name="departmentemaillog",
            name="t_departmen_respons_4392d2_idx",
        ),
        migrations.AlterUniqueTogether(
            name="departmentemaillog",
            unique_together={
                ("responsible_department", "recipient_email", "email_type", "send_date")
            },
        ),
        migrations.AddIndex(
            model_name="departmentemaillog",
            index=models.Index(
                fields=[
                    "responsible_department",
                    "recipient_email",
                    "email_type",
                    "send_date",
                ],
                name="t_departmen_respons_abf61e_idx",
            ),
        ),
    ]
