#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File     : serializers.py
<AUTHOR> JT_DA
@Date     : 2025/04/23
@File_Desc: 
'''

from rest_framework import serializers

from apps.monitor_registry.tasks.data_processing import get_monitor_registry_file_by_id


choices = [
    ("monitor_registry", get_monitor_registry_file_by_id),
]


class FileDownloadSerializer(serializers.Serializer):
    method_name = serializers.ChoiceField(choices=[i[0] for i in choices])
    file_id = serializers.IntegerField()