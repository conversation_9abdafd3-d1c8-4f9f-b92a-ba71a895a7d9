from django.db import models
from internal_control.base_model import BaseModel
from internal_control.settings import UPLOAD_DIR


class MonitorRegistryFile(BaseModel):
    file = models.FileField(upload_to=UPLOAD_DIR, verbose_name="附件")
    file_name = models.CharField(max_length=255, verbose_name="附件原名")

    class Meta:
        verbose_name = "监控列表附件"
        verbose_name_plural = verbose_name
        db_table = "t_monitor_registry_file"


class MonitorRegistry(BaseModel):
    # 监控项目
    monitor_project = models.CharField(max_length=255, blank=True, null=True, verbose_name="监控项目")
    # 监控要点
    monitor_point = models.TextField(blank=True, null=True, verbose_name="监控要点")
    # 监控方法
    monitor_method = models.TextField(blank=True, null=True, verbose_name="监控方法")
    # 负责部门
    responsible_department = models.ForeignKey(
        "dictionary.DictItem",
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        verbose_name="负责部门",
        related_name="monitor_registry_responsible_department",
    )
    # 监控层级
    monitor_level = models.ForeignKey(
        "dictionary.DictItem",
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        verbose_name="监控层级",
        related_name="monitor_registry_monitor_level",
    )
    # 监控开关
    monitor_switch = models.BooleanField(default=False, verbose_name="监控开关")
    # 监控周期
    monitor_cycle = models.ForeignKey(
        "dictionary.DictItem",
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        verbose_name="监控周期",
        related_name="monitor_registry_monitor_cycle",
    )
    # 附件
    attachment = models.ManyToManyField(
        "monitor_registry.MonitorRegistryFile",
        blank=True,
        verbose_name="附件",
        related_name="monitor_registry_attachment",
    )

    class Meta:
        verbose_name = "监控列表"
        verbose_name_plural = verbose_name
        db_table = "t_monitor_registry"
        # 确保同一负责部门内的监控项目名称唯一
        unique_together = ["monitor_project", "responsible_department"]

    def __str__(self):
        return self.monitor_project


class MonitorRegistryDevelopment(BaseModel):
    monitor_registry = models.ForeignKey(
        "monitor_registry.MonitorRegistry",
        on_delete=models.CASCADE,
        verbose_name="监控列表",
        related_name="monitor_registry_development",
    )
    # 开展日期
    development_date = models.DateTimeField(null=True, blank=True, verbose_name="开展日期")
    # 开展内容
    development_content = models.TextField(null=True, blank=True, verbose_name="开展内容")

    class Meta:
        verbose_name = "监控列表开展情况"
        verbose_name_plural = verbose_name
        db_table = "t_monitor_registry_development"  # 表名也同步修改

    def __str__(self):
        return self.monitor_registry.monitor_project


class DepartmentEmailLog(BaseModel):
    """部门邮件发送日志模型 - 按负责部门合并发送邮件"""

    # 邮件类型选择
    EMAIL_TYPE_CHOICES = [
        ("ALERT", "提醒邮件"),
        ("OVERDUE", "逾期提醒邮件"),
        ("ADMIN_NOTIFICATION", "管理员通知邮件"),
    ]
    # 发送状态选择
    SEND_STATUS_CHOICES = [
        ("PENDING", "待发送"),
        ("SENDING", "发送中"),
        ("SUCCESS", "发送成功"),
        ("FAILED", "发送失败"),
    ]
    # 负责部门
    responsible_department = models.ForeignKey(
        "dictionary.DictItem", on_delete=models.CASCADE, verbose_name="负责部门", related_name="department_email_logs"
    )
    # 邮件类型
    email_type = models.CharField(max_length=20, choices=EMAIL_TYPE_CHOICES, verbose_name="邮件类型")
    # 监控项目列表（多对多关联）
    monitor_registries = models.ManyToManyField(
        "monitor_registry.MonitorRegistry",
        verbose_name="监控项目列表",
        help_text="包含需要发送邮件的所有监控项目",
        related_name="department_email_logs",
    )
    # 收件人邮箱
    recipient_email = models.EmailField(max_length=254, verbose_name="收件人邮箱")
    # 邮件主题
    email_subject = models.CharField(max_length=500, verbose_name="邮件主题")
    # 邮件内容
    email_content = models.TextField(verbose_name="邮件内容")
    # 发送状态
    send_status = models.CharField(
        max_length=20, choices=SEND_STATUS_CHOICES, default="PENDING", verbose_name="发送状态"
    )

    # 发送尝试次数
    send_count = models.IntegerField(default=0, verbose_name="发送尝试次数")

    # 最后一次错误信息
    last_error = models.TextField(null=True, blank=True, verbose_name="最后错误信息")

    # 预定发送时间
    scheduled_send_time = models.DateTimeField(verbose_name="预定发送时间")

    # 实际发送时间
    actual_send_time = models.DateTimeField(null=True, blank=True, verbose_name="实际发送时间")

    # 发送日期
    send_date = models.DateField(verbose_name="发送日期")

    class Meta:
        verbose_name = "部门邮件发送日志"
        verbose_name_plural = verbose_name
        db_table = "t_department_email_log"
        # 确保同一业务人员在同一天的同类邮件只能有一条记录
        unique_together = ["responsible_department", "recipient_email", "email_type", "send_date"]
        # 添加索引优化查询性能
        indexes = [
            models.Index(fields=["responsible_department", "recipient_email", "email_type", "send_date"]),
        ]

    def __str__(self):
        department_name = self.responsible_department.dict_info if self.responsible_department else "未知部门"
        project_count = self.monitor_registries.count()
        return f"{department_name} - {self.get_email_type_display()} ({project_count}个项目) - {self.send_date}"

    def get_project_list(self):
        """获取项目名称列表"""
        return [registry.monitor_project for registry in self.monitor_registries.all()]

    def get_project_list_display(self):
        """获取项目列表的显示格式：【项目1】、【项目2】、【项目N】"""
        project_list = self.get_project_list()
        if not project_list:
            return ""
        return "、".join([f"【{project}】" for project in project_list])
