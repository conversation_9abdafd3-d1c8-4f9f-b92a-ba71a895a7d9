#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : serializers.py
<AUTHOR> JT_DA
@Date     : 2025/04/15
@File_Desc:
"""

from rest_framework import serializers

from apps.dictionary.models import DictItem, DictType


class DictItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = DictItem
        fields = "__all__"


class DictTypeSerializer(serializers.ModelSerializer):
    items = DictItemSerializer(many=True, read_only=True)

    class Meta:
        model = DictType
        fields = "__all__"
