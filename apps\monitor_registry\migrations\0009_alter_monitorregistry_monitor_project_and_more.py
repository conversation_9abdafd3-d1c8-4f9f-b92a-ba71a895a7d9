# Generated by Django 4.1.13 on 2025-07-09 15:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("dictionary", "0007_auto_20250708_1631"),
        ("monitor_registry", "0008_departmentemaillog_delete_monitoremaillog_and_more"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="monitorregistry",
            name="monitor_project",
            field=models.CharField(
                blank=True, max_length=255, null=True, verbose_name="监控项目"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="monitorregistry",
            unique_together={("monitor_project", "responsible_department")},
        ),
    ]
