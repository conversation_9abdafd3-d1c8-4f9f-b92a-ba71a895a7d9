#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : pagination.py
<AUTHOR> JT_DA
@Date     : 2025/04/15
@File_Desc:
"""

from rest_framework.pagination import PageNumberPagination

from utils.ajax_result import AjaxResult


class MyPageNumberPagination(PageNumberPagination):
    page_size = 5  # default page size
    page_query_param = "page"
    page_size_query_param = "page_size"
    max_page_size = 9999  # max page size

    def get_paginated_response(self, data):
        data = {
            "links": {"next": self.get_next_link(), "previous": self.get_previous_link()},
            "count": self.page.paginator.count,
            "results": data,
        }
        return AjaxResult.success(data=data)
