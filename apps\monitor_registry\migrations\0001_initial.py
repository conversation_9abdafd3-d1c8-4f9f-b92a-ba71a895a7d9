# Generated by Django 4.1.13 on 2025-04-18 16:10

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("dictionary", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="MonitorRegistry",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("revision", models.IntegerField(default=1, verbose_name="版本号")),
                ("created_by", models.IntegerField(null=True, verbose_name="创建人")),
                (
                    "created_time",
                    models.DateTimeField(
                        auto_now_add=True, null=True, verbose_name="创建时间"
                    ),
                ),
                ("updated_by", models.IntegerField(null=True, verbose_name="更新人")),
                (
                    "updated_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "monitor_project",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="监控项目"
                    ),
                ),
                (
                    "monitor_point",
                    models.TextField(blank=True, null=True, verbose_name="监控要点"),
                ),
                (
                    "monitor_method",
                    models.TextField(blank=True, null=True, verbose_name="监控方法"),
                ),
                (
                    "monitor_switch",
                    models.BooleanField(default=False, verbose_name="监控开关"),
                ),
                (
                    "involved_unit",
                    models.ManyToManyField(
                        blank=True,
                        related_name="monitor_registry_involved_unit",
                        to="dictionary.dictitem",
                        verbose_name="涉及单位",
                    ),
                ),
                (
                    "monitor_cycle",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="monitor_registry_monitor_cycle",
                        to="dictionary.dictitem",
                        verbose_name="监控周期",
                    ),
                ),
                (
                    "responsible_department",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="monitor_registry_responsible_department",
                        to="dictionary.dictitem",
                        verbose_name="负责部门",
                    ),
                ),
            ],
            options={
                "verbose_name": "监控列表",
                "verbose_name_plural": "监控列表",
                "db_table": "t_monitor_registry",
            },
        ),
        migrations.CreateModel(
            name="MonitorRegistryDevelopment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("revision", models.IntegerField(default=1, verbose_name="版本号")),
                ("created_by", models.IntegerField(null=True, verbose_name="创建人")),
                (
                    "created_time",
                    models.DateTimeField(
                        auto_now_add=True, null=True, verbose_name="创建时间"
                    ),
                ),
                ("updated_by", models.IntegerField(null=True, verbose_name="更新人")),
                (
                    "updated_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "development_date",
                    models.DateField(blank=True, null=True, verbose_name="开展日期"),
                ),
                (
                    "development_content",
                    models.TextField(blank=True, null=True, verbose_name="开展内容"),
                ),
                (
                    "monitor_registry",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="monitor_registry_development",
                        to="monitor_registry.monitorregistry",
                        verbose_name="监控列表",
                    ),
                ),
            ],
            options={
                "verbose_name": "监控列表开展情况",
                "verbose_name_plural": "监控列表开展情况",
                "db_table": "t_monitor_registry_development",
            },
        ),
    ]
