#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : dict_item_list_view.py
<AUTHOR> JT_DA
@Date     : 2025/04/15
@File_Desc:
"""

from rest_framework import generics

from utils.ajax_result import AjaxResult
from apps.dictionary.models import DictItem, DictType
from apps.dictionary.serializers import DictItemSerializer


class DictItemListView(generics.ListAPIView):
    queryset = DictItem.objects.select_related("dict_type").all()
    serializer_class = DictItemSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        type_code = self.kwargs["type_code"]
        try:
            queryset = queryset.filter(dict_type__type_code=type_code)
        except DictType.DoesNotExist:
            queryset = queryset.none()
        return queryset

    def build_tree(self, items, parent_id=None):
        tree = []
        for item in items:
            if item["parent"] == parent_id:
                children = self.build_tree(items, item["id"])
                tree_item = {
                    "value": item["id"],
                    "label": item["dict_info"],
                }
                if children:
                    tree_item["children"] = children
                tree.append(tree_item)
        return tree

    def get(self, request, *args, **kwargs):
        """
        获取字典

            type_code:
            functional_department, 职能部门
            district_customs, 直属海关
            monitor_cycle, 监控周期
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)

        parent_id = self.kwargs.get("parent_id", None)

        # 构建字典类型的数据结构
        data = self.build_tree(serializer.data, parent_id)
        return AjaxResult.success(data=data)
