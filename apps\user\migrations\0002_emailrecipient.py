# Generated by Django 4.1.13 on 2025-07-09 15:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("user", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="EmailRecipient",
            fields=[
                (
                    "id",
                    models.AutoField(
                        primary_key=True, serialize=False, verbose_name="id"
                    ),
                ),
                (
                    "group_name",
                    models.Char<PERSON>ield(max_length=255, verbose_name="部门名称"),
                ),
                (
                    "real_name",
                    models.<PERSON>r<PERSON>ield(max_length=255, verbose_name="真实姓名"),
                ),
            ],
            options={
                "verbose_name": "邮件接收用户",
                "db_table": "t_email_recipient",
            },
        ),
    ]
