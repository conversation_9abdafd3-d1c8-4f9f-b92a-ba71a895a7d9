#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : __init__.py
<AUTHOR> JT_DA
@Date     : 2025/04/15
@File_Desc:
"""


class TupleConstant:
    _instances = {}

    def __new__(cls, tuple_list, *args, **kwargs):
        instance_key = (cls, tuple_list)
        if instance_key not in cls._instances:
            cls._instances[instance_key] = super().__new__(cls)
        return cls._instances[instance_key]

    def __init__(self, tuple_list, *args, **kwargs):
        if not hasattr(self, "tuple_list"):
            self.tuple_list = tuple_list
            self.number_to_value = {k: v for k, v in tuple_list}
            self.value_to_number = {v: k for k, v in tuple_list}

    def __getitem__(self, key):
        if isinstance(key, str):
            key = key.strip()
            return self.value_to_number[key]
        elif isinstance(key, int):
            return self.number_to_value[key]

    def choices(self):
        return self.tuple_list

    def get_list(self):
        return [v for k, v in self.tuple_list]
