#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : data_processing.py
<AUTHOR> JT_DA
@Date     : 2025/01/16
@File_Desc: 监控注册数据处理相关函数
"""

from dateutil import parser

from apps.dictionary.models import DictItem
from apps.monitor_registry.models import MonitorRegistryFile, MonitorRegistry


def get_monitor_registry_file_by_id(file_id):
    """
    获取监控列表文件

    :param file_id:
    :return:
    """
    return MonitorRegistryFile.objects.get(pk=file_id)


def parse_date(param):
    """
    解析日期参数，支持中文格式日期
    
    :param param: 日期参数
    :return: 解析后的日期对象，失败返回None
    """
    try:
        if not param:
            return None
        date_str = str(param)
        # 将中文日期转换为英文日期格式
        date_str = date_str.replace("年", "-").replace("月", "-").replace("日", "")
        date_str = date_str.replace("时", ":").replace("分", ":").replace("秒", "")
        return parser.parse(date_str)
    except ValueError:
        return None


def get_dict_item_id(dict_info, type_code=None, many=False):
    """
    根据字典信息获取字典项ID
    
    :param dict_info: 字典信息字符串
    :param type_code: 字典类型代码
    :param many: 是否支持多个值
    :return: 字典项ID列表或单个ID
    """
    if "/" in dict_info and many:
        dict_info = dict_info.replace("/", ";")
    else:
        dict_info = dict_info.split("/")[-1]
    dict_info_list = dict_info.split(";")
    ids = list(
        DictItem.objects.filter(dict_type__type_code=type_code, dict_info__in=dict_info_list).values_list(
            "id", flat=True
        )
    )
    return ids if ids else None


def get_monitor_switch(monitor_switch_str):
    """
    解析监控开关状态
    
    :param monitor_switch_str: 开关状态字符串
    :return: 布尔值
    """
    if monitor_switch_str == "启动":
        return True
    elif monitor_switch_str == "关闭":
        return False
    else:
        return False


def get_monitor_registry(monitor_project_str):
    """
    根据项目名称获取监控注册记录ID
    
    :param monitor_project_str: 监控项目名称
    :return: 监控注册记录ID，不存在返回None
    """
    if MonitorRegistry.objects.filter(monitor_project=monitor_project_str).exists():
        return MonitorRegistry.objects.get(monitor_project=monitor_project_str).id
    else:
        return None


def process_row_data_for_sheet1(row, request):
    """
    处理第一个工作表的行数据
    
    :param row: 行数据字典
    :param request: 请求对象
    :return: 处理后的数据字典
    """
    col_map_func = {
        "监控项目": {
            "field_name": "monitor_project",
            "func_name": None,
            "args": [],
            "return_type": "str",
        },
        "监控要点": {
            "field_name": "monitor_point",
            "func_name": None,
            "args": [],
            "return_type": "str",
        },
        "监控方法": {
            "field_name": "monitor_method",
            "func_name": None,
            "args": [],
            "return_type": "str",
        },
        "责任主体": {
            "field_name": "responsible_department",
            "func_name": get_dict_item_id,
            "args": ["functional_department"],
            "return_type": "int",
        },
        "监控层级": {
            "field_name": "monitor_level",
            "func_name": get_dict_item_id,
            "args": ["monitor_level"],
            "return_type": "int",
        },
        "启动/关闭": {
            "field_name": "monitor_switch",
            "func_name": get_monitor_switch,
            "args": [],
            "return_type": "bool",
        },
        "监控周期": {
            "field_name": "monitor_cycle",
            "func_name": get_dict_item_id,
            "args": ["monitor_cycle"],
            "return_type": "int",
        },
    }

    item = {}
    for col_name, col_info in col_map_func.items():
        if col_name in row and row[col_name]:
            value = row[col_name].strip() if isinstance(row[col_name], str) else row[col_name]
            item_value = col_info["func_name"](value, *col_info["args"]) if col_info["func_name"] else value

            if isinstance(item_value, list) and col_info["return_type"] != "list":
                item_value = item_value[0] if item_value else None
                if col_info["return_type"] == "int":
                    item_value = int(item_value) if item_value is not None else None

            item[col_info["field_name"]] = item_value
    return item


def process_row_data_for_sheet2(row, request):
    """
    处理第二个工作表的行数据
    
    :param row: 行数据字典
    :param request: 请求对象
    :return: 处理后的数据字典
    """
    col_map_func = {
        "监控项目": {
            "field_name": "monitor_registry",
            "func_name": get_monitor_registry,
            "args": [],
            "return_type": "int",
        },
        "开展日期": {
            "field_name": "development_date",
            "func_name": parse_date,
            "args": [],
            "return_type": "date",
        },
        "开展内容": {
            "field_name": "development_content",
            "func_name": None,
            "args": [],
            "return_type": "str",
        },
    }

    item = {}
    for col_name, col_info in col_map_func.items():
        if col_name in row and row[col_name]:
            value = row[col_name].strip() if isinstance(row[col_name], str) else row[col_name]
            item_value = col_info["func_name"](value, *col_info["args"]) if col_info["func_name"] else value

            if isinstance(item_value, list) and col_info["return_type"] != "list":
                item_value = item_value[0] if item_value else None
                if col_info["return_type"] == "int":
                    item_value = int(item_value) if item_value is not None else None

            item[col_info["field_name"]] = item_value
    return item 