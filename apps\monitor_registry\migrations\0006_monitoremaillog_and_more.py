# Generated by Django 4.1.13 on 2025-06-24 14:52

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("monitor_registry", "0005_remove_monitorregistry_involved_unit"),
    ]

    operations = [
        migrations.CreateModel(
            name="MonitorEmailLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("revision", models.IntegerField(default=1, verbose_name="版本号")),
                ("created_by", models.IntegerField(null=True, verbose_name="创建人")),
                (
                    "created_time",
                    models.DateTimeField(
                        auto_now_add=True, null=True, verbose_name="创建时间"
                    ),
                ),
                ("updated_by", models.IntegerField(null=True, verbose_name="更新人")),
                (
                    "updated_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "email_type",
                    models.CharField(
                        choices=[("ALERT", "提醒邮件"), ("OVERDUE", "逾期提醒邮件")],
                        max_length=20,
                        verbose_name="邮件类型",
                    ),
                ),
                (
                    "period_identifier",
                    models.CharField(
                        help_text="用于标识邮件发送的业务周期",
                        max_length=50,
                        verbose_name="周期标识符",
                    ),
                ),
                (
                    "recipient_email",
                    models.EmailField(max_length=254, verbose_name="收件人邮箱"),
                ),
                (
                    "email_subject",
                    models.CharField(max_length=500, verbose_name="邮件主题"),
                ),
                ("email_content", models.TextField(verbose_name="邮件内容")),
                (
                    "send_status",
                    models.CharField(
                        choices=[
                            ("PENDING", "待发送"),
                            ("SENDING", "发送中"),
                            ("SUCCESS", "发送成功"),
                            ("FAILED", "发送失败"),
                        ],
                        default="PENDING",
                        max_length=20,
                        verbose_name="发送状态",
                    ),
                ),
                (
                    "send_count",
                    models.IntegerField(default=0, verbose_name="发送尝试次数"),
                ),
                (
                    "last_error",
                    models.TextField(
                        blank=True, null=True, verbose_name="最后错误信息"
                    ),
                ),
                (
                    "scheduled_send_time",
                    models.DateTimeField(verbose_name="预定发送时间"),
                ),
                (
                    "actual_send_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="实际发送时间"
                    ),
                ),
                (
                    "monitor_registry",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="email_logs",
                        to="monitor_registry.monitorregistry",
                        verbose_name="监控项目",
                    ),
                ),
            ],
            options={
                "verbose_name": "监控邮件发送日志",
                "verbose_name_plural": "监控邮件发送日志",
                "db_table": "t_monitor_email_log",
            },
        ),
        migrations.AddIndex(
            model_name="monitoremaillog",
            index=models.Index(
                fields=["send_status", "scheduled_send_time"],
                name="t_monitor_e_send_st_d347f4_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="monitoremaillog",
            index=models.Index(
                fields=["period_identifier", "email_type"],
                name="t_monitor_e_period__82cb07_idx",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="monitoremaillog",
            unique_together={("monitor_registry", "period_identifier", "email_type")},
        ),
    ]
