#!/bin/bash

set -e

# 此脚本用于在Jenkins中本地构建后端镜像（测试）并启动容器

# 挂载系统目录
PUBLIC_PATH="/home/<USER>"
SHARE_PATH="/home/<USER>"

# 定义常量
TAG=$(date +%Y%m%d)
IMAGE_NAME="internal_control_hg_fs:latest"
CONTAINER_NAME="internal-control-hg-fs-container"
PORT="11018"
DM8_PATH="/home/<USER>/dm8"
EXTERNAL_PATH="$PUBLIC_PATH/file_server/gdfs/internal_control_hg_fs"
CONFIG_PATH="$EXTERNAL_PATH/gunicorn_config.py"
UPLOAD_PATH="$EXTERNAL_PATH/upload"
LOG_PATH="$EXTERNAL_PATH/logs"
BACKUP_PATH="$EXTERNAL_PATH/backups"
TIMEZONE="Asia/Shanghai"
DEPLOY_PATH="$SHARE_PATH/4-运维部署/广东分署更新文件/广东分署$TAG"
BAK_BACKEND_SCRIPTS_PATH="$SHARE_PATH/4-运维部署/部署脚本/广东分署/后端"
BAK_FRONTEND_SCRIPTS_PATH="$SHARE_PATH/4-运维部署/部署脚本/广东分署/前端"
BACKEND_SCRIPT_NAME="internal_control_hg_fs_run.sh"
FRONTEND_SCRIPT_NAME="deploy_frontend_for_internal_control.sh"

# 准备工作区
echo "当前工作目录: $(pwd)"

# 函数：创建必要的目录
create_directories() {
    sudo mkdir -p "${EXTERNAL_PATH}"
    sudo mkdir -p "${UPLOAD_PATH}"
    sudo mkdir -p "${LOG_PATH}"
    sudo mkdir -p "${BACKUP_PATH}"
    sudo mkdir -p "${DEPLOY_PATH}"
}

# 函数：复制配置文件
copy_config_file() {
    sudo cp gunicorn_config_for_test.py "$CONFIG_PATH"
}

# 函数：检查并删除容器
check_and_remove_container() {
    if sudo docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        if sudo docker inspect -f '{{.State.Running}}' "${CONTAINER_NAME}" | grep -q "true"; then
            sudo docker stop "${CONTAINER_NAME}"
            echo "容器 ${CONTAINER_NAME} 已停止。"
        fi
        sudo docker rm "${CONTAINER_NAME}"
        echo "已删除容器 ${CONTAINER_NAME}。"
    fi
}

# 函数：判断并删除旧镜像
check_and_remove_image() {
    if sudo docker images --format '{{.Repository}}:{{.Tag}}' | grep -q "^${IMAGE_NAME}$"; then
        echo "镜像 ${IMAGE_NAME} 已存在，先删除该镜像。"
        sudo docker rmi "${IMAGE_NAME}"
    fi
}

# 函数：构建镜像
build_image() {
    sudo docker build -t "$IMAGE_NAME" .
}

# 函数：启动容器
run_container() {
    sudo docker run -d \
        --name "${CONTAINER_NAME}" \
        -p "${PORT}:${PORT}" \
        -v "${CONFIG_PATH}:/app/gunicorn_config.py" \
        -v "${DM8_PATH}:/dm8" \
        -v "${UPLOAD_PATH}:/app/upload" \
        -v "${LOG_PATH}:/app/logs" \
        -v "${BACKUP_PATH}:/app/backups" \
        -e TZ=${TIMEZONE} \
        "${IMAGE_NAME}"
}

# 函数：检查容器是否运行并显示日志
check_container_running() {
    sleep 10
    if sudo docker inspect -f '{{.State.Running}}' "${CONTAINER_NAME}" | grep -q "true"; then
        echo "以下是容器 ${CONTAINER_NAME} 的部分日志："
        sudo docker logs --tail 20 "${CONTAINER_NAME}"
        echo "容器 ${CONTAINER_NAME} 成功启动。"
    else
        echo "容器 ${CONTAINER_NAME} 未能成功启动。"
    fi
}

# 函数：清理未标记的Docker镜像
clean_none_images() {
    none_images=$(sudo docker images | grep "^<none>" | awk '{print $3}')
    if [ -n "$none_images" ]; then
        sudo docker rmi $none_images
    else
        echo "No <none> tagged images to remove."
    fi
}

# 函数：复制脚本文件
copy_scripts() {
    touch run_scripts.sh # 创建脚本文件
    echo "#!/bin/bash" > run_scripts.sh
    sudo cp -n run_scripts.sh "$DEPLOY_PATH" # -n 参数表示不覆盖已存在的文件
    sudo cp documents/deploy_scripts/"$BACKEND_SCRIPT_NAME" "$DEPLOY_PATH"
    sudo cp documents/deploy_scripts/"$FRONTEND_SCRIPT_NAME" "$DEPLOY_PATH"
    sudo cp documents/deploy_scripts/"$BACKEND_SCRIPT_NAME" "$BAK_BACKEND_SCRIPTS_PATH"
    sudo cp documents/deploy_scripts/"$FRONTEND_SCRIPT_NAME" "$BAK_FRONTEND_SCRIPTS_PATH"
}

# 函数：更改目录权限
change_permissions() {
    sudo chown -R share:nogroup "$DEPLOY_PATH"
    sudo chown -R nobody:nogroup "$PUBLIC_PATH"/file_server
}

# 执行步骤
create_directories
copy_config_file
check_and_remove_container
check_and_remove_image
build_image
run_container
check_container_running
clean_none_images
copy_scripts
change_permissions

echo "镜像已成功构建并容器已成功启动。"