#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : gunicorn_config_for_test.py
<AUTHOR> JT_DA
@Date     : 2025/05/06
@File_Desc:
"""

bind = "0.0.0.0:11018"
workers = 4
worker_class = "gevent"
reload = False


env = {
    # 是否开启debug模式，该模式主要用于调试
    "debug": True,
    "log_level": "INFO",
    "log_dir": "./logs",
    # 上传文件存储地址
    "upload_dir": "./upload",
    # 备份文件存储地址
    "backup_dir": "./backups",
    # 预览系统服务
    "kk_server_url": "http://************:8012/onlinePreview?url=",
    # 文件下载服务
    "download_url": "http://************:11018/file_download/",
    # 用户系统服务（后端）
    "auth_server_url": "http://************:11001",
    # 工作台系统服务（前端）
    "workbench_frontend_url": "http://************:9010/workbench",
    # 前端服务
    "frontend_server_url": "http://************:9010/internal_control",
    # 数据库配置
    "db_engine": "django_dmPython",
    "db_host": "************",
    "db_port": "5236",
    "db_user": "INTERNAL_CONTROL_HG_FS",
    "db_password": "INTERNAL_CONTROL_HG_FS_USER",
    "db_name": "INTERNAL_CONTROL_HG_FS",
    # 对接用户系统字段
    "client_id": "fbZeAX2rNAbxpUt9fSKDJudvwPlM0aZ9cSTliRzB",
    "client_secret": "ul6JOc1PTQYgdubxb5rbtYrQP3Tqpo0aVNrz4pK1vuOjQ3HNRR3zVQY9zyoxcdPKtM8Klav0UclZ5Nma1W6aEp5gwO2wg7TR49Qc9Gmh5h64vnlvjffvIGqWB7TdrKNi",
    # ip白名单
    "ip_white_list": "127.0.0.1,************,*************",  # 逗号分隔的ip列表
    # celery broker url
    "celery_broker_url": "redis://*************:6379/6",
    "celeryd_concurrency": 4,
    # cache url
    "cache_url": "redis://*************:6379/6",
    # 缓存配置
    "enable_cache": True,
    "cache_timeout": 60 * 15,
    "redis_host": "*************",
    "redis_port": "6379",
    "redis_db": "6",
}
