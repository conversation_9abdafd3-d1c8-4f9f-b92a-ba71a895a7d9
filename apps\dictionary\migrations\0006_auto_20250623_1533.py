# Generated by Django 4.1.13 on 2025-06-23 15:33

import os
import pandas as pd
from django.db import migrations, transaction
from django.conf import settings


def update_dict_item_email(apps, schema_editor):
    """
    从Excel文件更新已有DictItem的邮箱地址信息

    参数:
        apps: Django应用注册表，用于获取模型
        schema_editor: 数据库schema编辑器
    """
    DictType = apps.get_model("dictionary", "DictType")
    DictItem = apps.get_model("dictionary", "DictItem")

    # 获取文件路径
    excel_path = os.path.join(settings.BASE_DIR, "documents", "内控监控系统字典（20250623）.xlsx")

    try:
        # 检查文件是否存在
        if not os.path.exists(excel_path):
            print(f"Excel文件不存在: {excel_path}")
            return

        data = pd.read_excel(excel_path)

        # 验证数据结构
        required_columns = ["类型编码", "类型名称", "字典编码", "字典信息"]
        for col in required_columns:
            if col not in data.columns:
                print(f"缺少必需列: {col}")
                return

        # 检查是否有邮箱地址列
        if "邮箱地址" not in data.columns:
            print("Excel文件中未找到'邮箱地址'列")
            return

        # 使用事务进行批量更新
        with transaction.atomic():
            update_count = 0
            create_count = 0
            
            # 处理字典类型
            dict_types = {}
            dict_items = {}
            parent_relations = []

            # 第一次遍历: 创建或更新字典类型和字典项
            for _, row in data.iterrows():
                type_code = row["类型编码"]
                type_name = row["类型名称"]
                dict_code = row["字典编码"]
                dict_info = row["字典信息"]
                email = row["邮箱地址"] if pd.notna(row["邮箱地址"]) and row["邮箱地址"].strip() else None

                # 如果字典类型不存在，则创建
                if type_code not in dict_types:
                    dict_type, _ = DictType.objects.get_or_create(
                        type_code=type_code, 
                        defaults={'type_name': type_name}
                    )
                    dict_types[type_code] = dict_type
                else:
                    dict_type = dict_types[type_code]

                # 尝试查找已有的字典项
                try:
                    dict_item = DictItem.objects.get(
                        dict_type=dict_type, 
                        dict_code=dict_code
                    )
                    # 更新已有字典项的邮箱地址
                    if email:
                        dict_item.email = email
                        dict_item.save(update_fields=['email'])
                        update_count += 1
                        print(f"更新邮箱: {dict_info} -> {email}")
                except DictItem.DoesNotExist:
                    # 创建新的字典项
                    dict_item = DictItem.objects.create(
                        dict_type=dict_type, 
                        dict_code=dict_code, 
                        dict_info=dict_info,
                        email=email
                    )
                    create_count += 1
                    if email:
                        print(f"新建条目（含邮箱）: {dict_info} -> {email}")

                # 存储字典项以便后续处理父子关系
                item_key = f"{type_code}_{dict_code}"
                dict_items[item_key] = dict_item

                # 记录父子关系
                parent = row["父级"]
                if pd.notna(parent) and parent:
                    parent_key = f"{type_code}_{parent}"
                    parent_relations.append((dict_item, parent_key))

            # 第二次遍历: 处理父子关系
            for dict_item, parent_key in parent_relations:
                if parent_key in dict_items:
                    parent_item = dict_items[parent_key]
                    dict_item.parent = parent_item
                    dict_item.save(update_fields=['parent'])

            print(f"邮箱地址更新完成: 更新 {update_count} 条记录，新建 {create_count} 条记录")

    except Exception as e:
        print(f"导入过程中发生错误: {str(e)}")
        # 如果出现错误，继续传播异常
        raise


class Migration(migrations.Migration):

    dependencies = [
        ("dictionary", "0005_dictitem_email"),
    ]

    operations = [
        migrations.RunPython(update_dict_item_email, reverse_code=migrations.RunPython.noop),
    ]
