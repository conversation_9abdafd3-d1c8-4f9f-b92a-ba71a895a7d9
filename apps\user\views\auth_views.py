#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : auth_views.py
<AUTHOR> JT_DA
@Date     : 2025/04/15
@File_Desc:
"""


import requests
from drf_spectacular.utils import extend_schema, OpenApiParameter
from ipware import get_client_ip
from rest_framework.views import APIView

from internal_control.constant import <PERSON><PERSON><PERSON>ey
from internal_control.settings import OAUTH2_PROVIDER, AUTH_SERVER_URL
from utils.ajax_result import AjaxResult
from utils.cache_manager import CacheManager
from utils.user_helper import request_auth_user_info


class MyAuthToken(APIView):
    authentication_classes = []
    permission_classes = []

    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="Authorization",
                description="Authorization token",
                type=str,
                location="header",
            )
        ]
    )
    def get(self, request):
        h4a_token = request.headers.get("Authorization")
        if not h4a_token:
            return AjaxResult.unauthorized()

        result = self.validate_token_and_get_user_info(h4a_token, request)
        if isinstance(result, dict):
            return AjaxResult.success(data=result)
        return AjaxResult.fail(msg=result)

    def validate_token_and_get_user_info(self, token, request):
        # Check for fake token for testing
        fake_token_map = {
            "wx": ["t3LrbfXMJdRgnQfkeFV286fZSDIEUC", "t3LrbfXMJdRgnQfkeFV286fZSDIEUC"],
        }
        if token in fake_token_map:
            return self.get_user_info_from_fake_token(token, fake_token_map, request)

        # Real token validation
        return self.get_user_info_from_real_token(token, request)

    def get_user_info_from_fake_token(self, token, fake_token_map, request):
        access_token, refresh_token = fake_token_map[token]
        auth_token = "Bearer " + access_token
        user_info = request_auth_user_info(request, auth_token)
        if not all([user_info.get("group_id"), user_info.get("role_id")]):
            return None
        return {
            "access_token": access_token,
            "token_type": "Bearer",
            "expires_in": "360000",
            "refresh_token": refresh_token,
            **user_info,
        }

    def get_user_info_from_real_token(self, token, request):
        try:
            auth_token_and_user_info_cache = CacheManager(RedisKey.AUTH_TOKEN_AND_USER_INFO)
            auth_token_and_user_info = auth_token_and_user_info_cache.get(token)
            if auth_token_and_user_info:
                return auth_token_and_user_info

            client_id, client_secret = OAUTH2_PROVIDER["RESOURCE_SERVER_INTROSPECTION_CREDENTIALS"]
            url = f"{AUTH_SERVER_URL}/h4a/handle-token/"
            client_ip, _ = get_client_ip(request)
            headers = {"X-Real-IP": client_ip}
            querystring = {
                "token": token,
                "client_id": client_id,
                "client_secret": client_secret,
            }
            response = requests.post(url, headers=headers, data=querystring)
            if response.status_code != 200:
                return response.json().get("msg")
            response_data = response.json().get("data")
            if not response_data:
                return None

            auth_token = response_data["token_type"] + " " + response_data["access_token"]
            user_info = request_auth_user_info(request, auth_token)
            if not all([user_info.get("group_id"), user_info.get("role_id")]):
                return None

            auth_token_and_user_info_cache.set(token, {**response_data, **user_info})

            return {**response_data, **user_info}
        except Exception:
            return None


class GetNewAccessToken(APIView):

    def get(self, request):
        ori_auth_token = request.headers.get("Authorization")
        refresh_token = request.GET.get("refresh_token")
        if not refresh_token:
            return AjaxResult.unauthorized()

        client_id, client_secret = OAUTH2_PROVIDER.get("RESOURCE_SERVER_INTROSPECTION_CREDENTIALS")
        response = requests.post(
            f"{AUTH_SERVER_URL}/token/",
            headers={"Authorization": ori_auth_token},
            data={
                "refresh_token": refresh_token,
                "client_id": client_id,
                "client_secret": client_secret,
            },
        )

        if response.status_code == 200 and (response_data := response.json().get("data")):
            return AjaxResult.success(data=response_data)
        else:
            return AjaxResult.unauthorized()
