#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : monitor_cycle.py
<AUTHOR> JT_DA
@Date     : 2025/01/16
@File_Desc: 监控周期检查和验证相关函数
"""

import calendar
from datetime import datetime

from apps.monitor_registry.models import MonitorRegistryDevelopment

# 周期月份常量定义
QUARTER_END_MONTHS = [3, 6, 9, 12]
HALF_YEAR_END_MONTHS = [6, 12]
YEAR_END_MONTHS = [12]


def is_period_end(date, months=None):
    """
    检查给定日期是否为指定周期的最后一天

    :param date: 要检查的日期
    :param months: 指定的月份列表，如果为None则检查任意月末
    :return: 是否为周期末
    """
    # 首先检查是否为当月最后一天
    last_day = calendar.monthrange(date.year, date.month)[1]
    is_month_last_day = date.day == last_day

    # 如果不是月末，直接返回False
    if not is_month_last_day:
        return False

    # 如果没有指定月份，返回是否为月末
    if months is None:
        return True

    # 检查当前月份是否在指定的月份列表中
    return date.month in months


def calculate_previous_period_end_date(current_date, cycle_type):
    """
    根据当前日期和监控周期类型计算上一期的期末日期

    :param current_date: 当前日期
    :param cycle_type: 监控周期类型 ('每月监控', '季度监控', '半年监控', '年度监控')
    :return: 上一期的期末日期，如果监控周期类型不支持则返回None
    """
    year = current_date.year
    month = current_date.month

    if cycle_type == "每月监控":
        # 上个月的最后一天
        if month == 1:
            previous_year = year - 1
            previous_month = 12
        else:
            previous_year = year
            previous_month = month - 1
        last_day = calendar.monthrange(previous_year, previous_month)[1]
        return datetime(previous_year, previous_month, last_day).date()

    elif cycle_type == "季度监控":
        # 上一季度的最后一天
        current_quarter = (month - 1) // 3 + 1
        if current_quarter == 1:
            # 当前是第一季度，上一季度是去年第四季度
            previous_year = year - 1
            previous_quarter_end_month = 12
        else:
            previous_year = year
            previous_quarter_end_month = (current_quarter - 1) * 3
        last_day = calendar.monthrange(previous_year, previous_quarter_end_month)[1]
        return datetime(previous_year, previous_quarter_end_month, last_day).date()

    elif cycle_type == "半年监控":
        # 上一半年的最后一天
        if month <= 6:
            # 当前是上半年，上一半年是去年下半年
            previous_year = year - 1
            previous_half_year_end_month = 12
        else:
            # 当前是下半年，上一半年是今年上半年
            previous_year = year
            previous_half_year_end_month = 6
        last_day = calendar.monthrange(previous_year, previous_half_year_end_month)[1]
        return datetime(previous_year, previous_half_year_end_month, last_day).date()

    elif cycle_type == "年度监控":
        # 上一年度的最后一天
        previous_year = year - 1
        last_day = calendar.monthrange(previous_year, 12)[1]
        return datetime(previous_year, 12, last_day).date()

    else:
        # 不支持的监控周期类型，返回None跳过处理
        return None


def check_development_records_in_range(monitor_registry, start_date, end_date):
    """
    检查指定时间范围内是否存在开展情况记录

    :param monitor_registry: 监控注册记录
    :param start_date: 开始日期
    :param end_date: 结束日期
    :return: 是否存在记录
    """
    return MonitorRegistryDevelopment.objects.filter(
        monitor_registry=monitor_registry, created_time__gte=start_date, created_time__lte=end_date
    ).exists()
