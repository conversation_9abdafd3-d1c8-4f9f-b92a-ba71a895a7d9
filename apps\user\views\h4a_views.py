#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : h4a_views.py
<AUTHOR> JT_DA
@Date     : 2025/04/15
@File_Desc:
"""

import json

import requests
from rest_framework.views import APIView

from internal_control.settings import AUTH_SERVER_URL
from utils.ajax_result import AjaxResult


class GetH4AUserInfo(APIView):
    def get(self, request):
        try:
            auth_token = request.headers.get("Authorization")
            if not auth_token:
                return {}
            url = f"{AUTH_SERVER_URL}/h4a/h4a_user_info/"
            headers = {"Authorization": auth_token}
            display_name = request.GET.get("display_name")
            if not display_name:
                return AjaxResult.success(data=[])
            params = {"display_name": display_name}
            response = requests.request("GET", url, headers=headers, params=params)
            response_text = json.loads(response.text)
            all_h4a_user_info = response_text.get("data").get("results")
            return AjaxResult.success(data=all_h4a_user_info)
        except Exception as e:
            return AjaxResult.success(data=[])
