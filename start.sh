#!/bin/sh
# 设置在遇到错误时退出
set -e

# 安装dmPython依赖并处理可能的错误
cd /app/documents/dm_driver_linux/dmPython
if ! python setup.py install; then
  echo "Failed to install dmPython"
  exit 1
fi

# 安装django_dmPython依赖并处理可能的错误
cd /app/documents/dm_driver_linux/django_dmPython3.0.0/django_dmPython
if ! python setup.py install; then
  echo "Failed to install django_dmPython"
  exit 1
fi

# 执行数据库迁移
cd /app
if ! python manage.py migrate; then
  echo "Failed to migrate database"
  exit 1
fi

# 启动supervisord
exec supervisord -c /etc/supervisor/conf.d/supervisord.conf