"""
Django settings for internal_control project.

Generated by 'django-admin startproject' using Django 4.1.13.

For more information on this file, see
https://docs.djangoproject.com/en/4.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.1/ref/settings/
"""

import os
from pathlib import Path

from gunicorn_config import env

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

config = os.environ.__dict__
config.update(env)

IMPORT_DIR = "templates/import_templates"
EXPORT_DIR = "templates/export_templates"


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-dis_shna658k0v1i_i8vhi##g$kae$zx7wj9i+w8fhg9!)g=4&"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = config.get("debug", True)

ALLOWED_HOSTS = ["*"]

# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django_celery_results",
    "celery",
    "rest_framework",
    "django_filters",
    "cacheops",
    "apps.dictionary",
    "apps.user",
    "apps.monitor_registry",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "internal_control.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [os.path.join(BASE_DIR, r"templates")],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "internal_control.wsgi.application"


# Database
# https://docs.djangoproject.com/en/4.1/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": config.get("db_engine"),
        "NAME": config.get("db_name"),
        "USER": config.get("db_user"),
        "PASSWORD": config.get("db_password"),
        "HOST": config.get("db_host"),
        "PORT": config.get("db_port"),
        # 'OPTIONS': {'local_code': 1, 'connection_timeout': 30}  # 必须！
    }
}


# Password validation
# https://docs.djangoproject.com/en/4.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.1/topics/i18n/

LANGUAGE_CODE = "zh-Hans"

TIME_ZONE = "Asia/Shanghai"

USE_I18N = True

USE_TZ = False


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.1/howto/static-files/

STATIC_ROOT = os.path.join(BASE_DIR, "static")
STATIC_URL = "static/"

# Default primary key field type
# https://docs.djangoproject.com/en/4.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

UPLOAD_DIR = config.get("upload_dir", BASE_DIR / "upload")
LOG_DIR = config.get("log_dir", BASE_DIR / "logs")
BACKUP_DIR = config.get("backup_dir", BASE_DIR / "backups")

for d in [UPLOAD_DIR, LOG_DIR, BACKUP_DIR]:
    os.makedirs(d, exist_ok=True)

# 日志配置
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "verbose": {
            "format": "[%(levelname)s] %(asctime)s %(module)s.%(funcName)s [%(process)d:%(thread)d] - %(filename)s[line:%(lineno)d] %(message)s",
        },
        "standard": {
            "format": "[%(levelname)s] %(asctime)s %(module)s - %(message)s",
        },
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "level": "DEBUG",
            "formatter": "standard",
            "stream": "ext://sys.stdout",
        },
        "null": {
            "level": "DEBUG",
            "class": "logging.NullHandler",
        },
        "file_handler": {
            "class": "concurrent_log_handler.ConcurrentRotatingFileHandler",
            "level": "DEBUG",
            "formatter": "verbose",
            "encoding": "utf8",
            "filename": os.path.join(LOG_DIR, "syslog.log"),
            "maxBytes": 1 * 1024 * 1024,  # 1 MB
            "backupCount": 5,
        },
    },
    "loggers": {
        "django": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": True,
        },
        "django.db.backends": {
            "handlers": ["null"],
            "level": "INFO",
            "propagate": False,
        },
        "django.request": {
            "handlers": ["console", "file_handler"],
            "level": "ERROR",
            "propagate": False,
        },
    },
    "root": {"level": config.get("log_level", "INFO"), "handlers": ["file_handler"]},
}

# Celery配置
# 设置任务接受的类型，默认是{'json'}
CELERY_ACCEPT_CONTENT = ["application/json"]
# 设置task任务序列列化为json
CELERY_TASK_SERIALIZER = "json"
# 请任务接受后存储时的类型
CELERY_RESULT_SERIALIZER = "json"
# 时间格式化为中国时间
CELERY_TIMEZONE = "Asia/Shanghai"
# 是否使用UTC时间
CELERY_ENABLE_UTC = False
# 指定broker为redis 如果指定rabbitmq CELERY_BROKER_URL = 'amqp://guest:guest@localhost:5672//'
CELERY_BROKER_URL = config.get("celery_broker_url", "redis://localhost:6379/0")
# 指定存储结果的地方，支持使用rpc、数据库、redis等等，具体可参考文档 # CELERY_RESULT_BACKEND = 'db+mysql://scott:tiger@localhost/foo' # mysql 作为后端数据库
# CELERY_RESULT_BACKEND = 'redis://127.0.0.1:6379/4'
# 使用django数据库存储结果 来自 django_celery_results
CELERY_RESULT_BACKEND = "django-db"
# 结果的缓存配置 来自 django_celery_results
# CELERY_CACHE_BACKEND = 'django-cache'
# 设置任务过期时间 默认是一天，为None或0 表示永不过期
CELERY_TASK_RESULT_EXPIRES = 60 * 60 * 24
# 设置异步任务结果永不过期，如果不设置的话，每天04点celery会自动清空过期的异步任务结果
# CELERY_RESULT_EXPIRES = 0
# 设置worker并发数，默认是cpu核心数
CELERYD_CONCURRENCY = config.get("celeryd_concurrency", 4)
# 设置每个worker最大任务数
CELERYD_MAX_TASKS_PER_CHILD = 100
# 使用队列分流每个任务
# CELERY_QUEUES = (
#     Queue("add", Exchange("add"), routing_key="task_add"),
#     Queue("mul", Exchange("mul"), routing_key="task_mul"),
#     Queue("xsum", Exchange("xsum"), routing_key="task_xsum"),
# )
# 配置队列分流路由，注意可能无效，需要在运行异步任务时来指定不同的队列
# CELERY_ROUTES = {
#     'public.tasks.add': {'queue': 'add', 'routing_key':'task_add'},
#     'public.tasks.mul': {'queue': 'add', 'routing_key':'task_add'},
#     'public.tasks.xsum': {'queue': 'add', 'routing_key':'task_add'},
#     # 'public.tasks.mul': {'queue': 'mul', 'routing_key':'task_mul'},
#     # 'public.tasks.xsum': {'queue': 'xsum', 'routing_key':'task_xsum'},
#     }

# django rest framework config
REST_FRAMEWORK = {
    # 默认登录认证
    "DEFAULT_AUTHENTICATION_CLASSES": ("utils.token_helper.MyOAuth2Authentication",),
    # 默认权限认证
    "DEFAULT_PERMISSION_CLASSES": ("utils.permission_helper.MyPermission",),
    # 默认异常处理器
    "EXCEPTION_HANDLER": "utils.exception_helper.custom_exception_handler",
    # 默认参数过滤器
    "DEFAULT_FILTER_BACKENDS": ("django_filters.rest_framework.DjangoFilterBackend",),
}

AUTH_SERVER_URL = config.get("auth_server_url", "")
FRONTEND_SERVER_URL = config.get("frontend_server_url", "")
WORKBENCH_FRONTEND_URL = config.get("workbench_frontend_url", "")
KK_SERVER_URL = config.get("kk_server_url", "")  # 预览系统服务
DOWNLOAD_URL = config.get("download_url", "")  # 文件下载服务

H4A_LOGIN_URL = f"{AUTH_SERVER_URL}/h4a/login/?login_url={FRONTEND_SERVER_URL}"

OAUTH2_PROVIDER = {
    "RESOURCE_SERVER_INTROSPECTION_URL": f"{AUTH_SERVER_URL}/introspect/",
    "RESOURCE_SERVER_INTROSPECTION_CREDENTIALS": (
        config.get("client_id", ""),
        config.get("client_secret", ""),
    ),
    # ('client_id', 'client_secret')
}

CUSTOMS_EMAIL = {
    "SENDER_EMAIL": config.get("sender_email", ""),
    "SMTP_SERVER": config.get("smtp_server", "smtp.qq.com"),
    "SMTP_PORT": config.get("smtp_port", 587),
    "USERNAME": config.get("smtp_username", ""),
    "PASSWORD": config.get("smtp_password", ""),
}

if DEBUG:
    INSTALLED_APPS += [
        "drf_spectacular",
    ]

    REST_FRAMEWORK["DEFAULT_SCHEMA_CLASS"] = "drf_spectacular.openapi.AutoSchema"
    REST_FRAMEWORK["COMPONENT_SPLIT_REQUEST"] = True

    SPECTACULAR_SETTINGS = {
        "TITLE": "广东分署内控风险监控清单API文档",
        "DESCRIPTION": "",
        "VERSION": "1.0.0",
        "SERVE_INCLUDE_SCHEMA": True,  # 包含请求参数的模式
        # 'COMPONENT_SPLIT_REQUEST': True,
        "SWAGGER_UI_SETTINGS": {
            "deepLinking": True,
            "persistAuthorization": True,
            "displayOperationId": True,
        },
    }

# IP白名单
IP_WHITE_LIST = config.get("ip_white_list", "127.0.0.1").split(",")

# 缓存配置
CACHE_TIMEOUT = 60 * 15
ENABLE_CACHE = config.get("enable_cache", True)
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": config.get("cache_url", "redis://localhost:6379/1"),  # Redis连接信息
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        },
    }
}

# CACHEOPS配置
CACHEOPS_REDIS = {
    "host": config.get("redis_host", "localhost"),
    "port": config.get("redis_port", 6379),
    "db": config.get("redis_db", 1),
    "socket_timeout": 3,  # 连接超时时间
    "retry_on_timeout": True,  # 超时时自动重试
    "max_connections": 50,  # 连接池最大连接数，4 workers × 10 并发 + 10 预留
    "socket_connect_timeout": 2,  # 建立连接超时时间
}

CACHEOPS = {
    "dictionary.*": {"ops": "all", "timeout": CACHE_TIMEOUT},
    "user.*": {"ops": "all", "timeout": CACHE_TIMEOUT},
}

# 当Redis连接失败时降级为不使用缓存
CACHEOPS_DEGRADE_ON_FAILURE = True

# 设置失败重试次数
CACHEOPS_DEFAULTS = {
    "timeout": CACHE_TIMEOUT,
    "retry_on_cache_miss": True,  # 缓存未命中时重试
    "cache_on_save": True,  # 保存时更新缓存
    "ops": "all",
}

# 系统名称
SYSTEM_NAME = "内控风险监控清单"
