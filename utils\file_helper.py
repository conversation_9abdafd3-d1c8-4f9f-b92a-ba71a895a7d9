#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : file_helper.py
<AUTHOR> JT_DA
@Date     : 2025/04/15
@File_Desc:
"""

import base64
import hashlib
import os
from io import BytesIO
from urllib.parse import quote

from django.http import FileResponse
from internal_control.settings import DOWNLOAD_URL, KK_SERVER_URL

from utils.ajax_result import AjaxResult


class FileDownloadHandler:
    @staticmethod
    def download_single_file(target, filename, echo_filename=None):
        """
        根据路径或BytesIO对象，下载单个文件

        :param target: 目标路径或BytesIO对象
        :param filename: 文件名称
        :param echo_filename: 显示名称
        :return:
        """
        try:
            # 如果target是字符串，则表示是路径，打开文件
            if isinstance(target, str):
                # 检查文件是否存在
                if not os.path.exists(target):
                    return AjaxResult.not_found()
                response = FileResponse(open(target, "rb"))
            else:
                # 如果target是BytesIO对象，则直接从该对象读取数据
                response = FileResponse(BytesIO(target.getvalue()))

            if echo_filename:
                filename = echo_filename

            # 打开文件并返回响应
            encoded_file_name = filename.encode("utf8").decode("ISO-8859-1")

            response["Content-Disposition"] = f"attachment;filename={encoded_file_name}"
            return response
        except Exception as e:
            msg = "下载错误，模板缺失。"
            return AjaxResult.fail(msg=msg)


# 根据文件名生成带有MD5哈希的文件名
def md5_hash_filename_with_extension(file_name):
    # 分割文件名和扩展名
    base_name, file_extension = os.path.splitext(os.path.basename(file_name))
    # 对文件名进行MD5哈希
    md5_hash = hashlib.md5(file_name.encode("utf-8")).hexdigest()
    # 返回哈希后的文件名和原始扩展名
    return f"{md5_hash}{file_extension}"


# 构建文件预览URL
def build_preview_url(method_name, file_id, file_name):
    try:
        # 对文件名进行MD5哈希处理
        file_name = md5_hash_filename_with_extension(file_name)
        # 构建编码后的URL
        encoded_url = base64.b64encode(
            f"{DOWNLOAD_URL}{method_name}/{file_id}/?fullfilename={file_name}".encode("utf-8")
        )
        # 返回完整的预览URL
        return f"{KK_SERVER_URL}{quote(encoded_url)}"
    except Exception:
        return ""
