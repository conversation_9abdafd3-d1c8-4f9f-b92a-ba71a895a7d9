# Generated by Django 4.1.13 on 2025-04-18 16:10

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="DictType",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("type_code", models.CharField(max_length=50, unique=True)),
                ("type_name", models.CharField(max_length=100)),
            ],
            options={
                "verbose_name": "字典类型",
                "verbose_name_plural": "字典类型",
                "db_table": "t_md_type",
            },
        ),
        migrations.CreateModel(
            name="DictItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("dict_code", models.<PERSON><PERSON><PERSON><PERSON>(max_length=50)),
                ("dict_info", models.Char<PERSON>ield(max_length=255)),
                (
                    "dict_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="items",
                        to="dictionary.dicttype",
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="children",
                        to="dictionary.dictitem",
                    ),
                ),
            ],
            options={
                "verbose_name": "字典明细",
                "verbose_name_plural": "字典明细",
                "db_table": "t_md_item",
            },
        ),
    ]
