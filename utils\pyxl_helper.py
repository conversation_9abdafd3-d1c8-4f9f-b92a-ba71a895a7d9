#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : xlsx_helper.py
<AUTHOR> JT_DA
@Date     : 2025/04/15
@File_Desc:
"""
import math
from io import BytesIO

import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side
from openpyxl.utils import column_index_from_string, get_column_letter


class XlsxHelper:
    def __init__(self, *args, **kwargs):
        self.source_path = kwargs.get("source_path")
        self.wb = openpyxl.load_workbook(self.source_path)
        self.ws = self.wb.active
        self.last_row_idx = self.ws.max_row

    def find_single_col_merge_set(self, col_index, start_row_idx, end_row_idx=None):
        """
        合并单元格

        :param col_index: 需要合并列标
        :param start_row_idx: 行开始位置
        :param end_row_idx: 行结束位置
        :return:
        """
        if not end_row_idx:
            end_row_idx = self.ws.max_row + 1
        merge_set = []
        content_list = [
            self.ws.cell(i, col_index).value
            for i in range(start_row_idx, end_row_idx)
            if self.ws.cell(i, col_index).value
        ]
        if content_list:
            # 判断合并单元格的始末位置
            start, end, flag = 0, 0, content_list[0]
            for i, content in enumerate(content_list):
                if content != flag:
                    flag = content
                    end = i - 1
                    if end >= start:
                        merge_set.append((start, start_row_idx, end, start_row_idx))
                        start = end + 1

                if content == " ":
                    flag = "*" + str(i)
                    start = i

                if i == len(content_list) - 1:
                    end = i
                    merge_set.append((start, start_row_idx, end, start_row_idx))

        return merge_set

    def find_merge_set_by_keyword(self, col_index, start_row_idx, end_row_idx, keyword):
        if not end_row_idx:
            end_row_idx = self.ws.max_row + 1
        # 使用列表推导式简化了合并集的创建
        return [(i, i, i, i) for i in range(start_row_idx, end_row_idx) if self.ws.cell(i, col_index).value == keyword]

    def merge_single_col_by_merge_set(self, col_name, merge_set, sub=None):
        # 使用f-string简化了字符串拼接
        for m_set in merge_set:
            self.ws.merge_cells(f"{col_name}{m_set[0] + m_set[1]}:{col_name}{m_set[2] + m_set[3]}")
            if sub:
                for col in sub:
                    self.merge_xlsx_col(col, [col], m_set[0] + m_set[1], end_row_idx=(m_set[2] + m_set[3]))

    def merge_mux_col_by_merge_set(self, col_name_set, merge_set):
        for m_set in merge_set:
            merge_range_start = f"{col_name_set[0]}{str(m_set[0])}"
            merge_range_end = f"{col_name_set[-1]}{str(m_set[0])}"
            merge_range = f"{merge_range_start}:{merge_range_end}"
            top_left_cell = self.ws[merge_range_start]
            # 使用列表推导式简化了值列表的创建
            top_left_cell.value = "".join(
                [
                    self.ws.cell(m_set[0], column_index_from_string(col_name)).value
                    for col_name in col_name_set
                    if self.ws.cell(m_set[0], column_index_from_string(col_name)).value
                ]
            )
            print(merge_range)
            self.ws.merge_cells(merge_range)

    def merge_xlsx_col(
        self, main_col, need_merge_cols, start_row_idx, end_row_idx=None, keyword=None, sub: list = None
    ):
        col_index = column_index_from_string(main_col)
        # 根据条件直接分配合适的函数给merge_func，然后直接使用它，从而避免了不必要的循环
        merge_set = (
            self.find_merge_set_by_keyword(col_index, start_row_idx, end_row_idx, keyword)
            if keyword
            else self.find_single_col_merge_set(col_index, start_row_idx, end_row_idx)
        )

        if keyword:
            self.merge_mux_col_by_merge_set(need_merge_cols, merge_set)
        else:
            for col in need_merge_cols:
                self.merge_single_col_by_merge_set(col, merge_set, sub)

    def xlsx_styles(self, start_row_idx, wrap_cols=None):
        """
        设置样式

        :param start_row_idx:
        :param wrap_cols: 指定换行的列
        :return:
        """

        max_row = self.last_row_idx

        if wrap_cols:
            wrap_cols = [column_index_from_string(col) for col in wrap_cols]
        else:
            wrap_cols = []

        # 已处理alignment的cell
        handled_cell = []

        # Calculate appropriate column widths
        column_widths = []
        for col in range(1, self.ws.max_column + 1):
            max_width = 1
            for row in range(start_row_idx - 1, max_row + 1):  # “start_row_idx - 1”包含标题行
                cell_value = str(self.ws.cell(row=row, column=col).value or "")
                try:
                    if col in wrap_cols:
                        cell_length = max([len(line.encode("gbk")) for line in cell_value.split("\n")])
                    else:
                        cell_length = len(cell_value.encode("gbk"))
                    if cell_length > 50:
                        if col not in wrap_cols:
                            self.ws.cell(row, col).alignment = Alignment(
                                wrapText=True, horizontal="left", vertical="center"
                            )
                            handled_cell.append((row, col))
                        cell_length = 50
                    max_width = max(max_width, cell_length)
                except:
                    max_width = 50
            column_widths.append(max_width)

        # Set column widths
        for index, width in enumerate(column_widths, start=1):
            column_letter = get_column_letter(index)  # 将数字转化为列名
            self.ws.column_dimensions[column_letter].width = (
                width + 2
            )  # 设置列宽，一般加两个字节宽度，可以根据实际情况灵活调整

        # Calculate appropriate row heights
        row_heights = []
        for row in range(start_row_idx, max_row + 1):
            max_height = 1
            for col in range(1, self.ws.max_column + 1):
                try:
                    cell_value = self.ws.cell(row=row, column=col).value
                    cell_value = str(cell_value) if cell_value else ""
                    len_sz = len(cell_value.encode("gbk"))
                    height = math.ceil(len_sz / 50) * 18 if len_sz > 50 else 18
                    height = max(height, len(cell_value.split("\n")) * 18)
                    max_height = max(max_height, height)
                except:
                    max_height = 18
            row_heights.append(max_height)

        # 设置行高
        for idx, height in enumerate(row_heights, start=start_row_idx):
            self.ws.row_dimensions[idx].height = height

        # 每个单元格内容居中、字体、边框样式
        font = Font(name="宋体", size="12", color="000000")
        border = Border(
            left=Side(style="thin"), bottom=Side(style="thin"), right=Side(style="thin"), top=Side(style="thin")
        )
        align = Alignment(horizontal="center", vertical="center")
        align_with_wrap = Alignment(wrapText=True, horizontal="center", vertical="center")

        for row in self.ws.iter_rows(min_row=start_row_idx, max_row=max_row):
            for cell in row:
                cell.font = font
                cell.border = border
                if (cell.row, cell.column) not in handled_cell:
                    cell.alignment = align if cell.column not in wrap_cols else align_with_wrap

        # 设置页脚
        self.ws.oddFooter.center.text = "第 &[Page] 页 共 &N 页"

    def data_write_in_sheet(self, data, col_set, start_row_idx=3, xh="A"):
        """
        数据写入表格

        :param data:
        :param col_set:
        :param start_row_idx:
        :param xh:
        :return:
        """
        serial_number = 1  # 序号

        for row_idx, sub in enumerate(data, start=start_row_idx):
            xh_col_inx = column_index_from_string(xh)
            self.ws.cell(row_idx, xh_col_inx).value = serial_number  # 序号
            serial_number += 1
            for k, v in sub.items():
                if col_set.get(k):
                    col_inx = column_index_from_string(col_set.get(k))
                    if isinstance(v, int):
                        self.ws.cell(row_idx, col_inx).value = v
                    elif isinstance(v, str):
                        self.ws.cell(row_idx, col_inx).value = str(v)
                    else:
                        self.ws.cell(row_idx, col_inx).value = v

            self.last_row_idx = row_idx

    # 生成合计行
    def generate_total_row(self, start_row_idx, col_set, xh="A"):
        """
        生成合计行

        :param start_row_idx:
        :param col_set:
        :param xh:
        :return:
        """
        total_row_idx = self.last_row_idx() + 1
        xh_col_inx = column_index_from_string(xh)
        self.ws.cell(total_row_idx, xh_col_inx).value = "合计"
        for k, v in col_set.items():
            col_inx = column_index_from_string(v)
            self.ws.cell(total_row_idx, col_inx).value = f"=SUM({v}{start_row_idx}:{v}{total_row_idx - 1})"

    def custom_total_row(self, start_row=None, start_column=1, end_row=None, end_column=1):
        if not start_row:
            start_row = self.last_row_idx
            end_row = start_row

        self.ws.cell(start_row, 1).value = "合计"
        self.ws.merge_cells(start_row=start_row, start_column=start_column, end_row=end_row, end_column=end_column)

    def set_xlsx_title(self, title):
        """
        设置标题（首行）

        :param title:
        :return:
        """
        self.ws.cell(1, 1).value = title

    def write_buffer(self):
        """
        将工作簿写入内存中的BytesIO对象

        :return:
        """
        buffer = BytesIO()
        self.wb.save(buffer)
        buffer.seek(0)
        return buffer
