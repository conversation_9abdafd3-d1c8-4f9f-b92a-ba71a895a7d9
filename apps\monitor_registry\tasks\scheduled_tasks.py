#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : scheduled_tasks.py
<AUTHOR> JT_DA
@Date     : 2025/01/16
@File_Desc: Celery定时任务相关函数 - 按负责部门合并发送邮件
"""

import logging
import time
from contextlib import contextmanager
from datetime import datetime

from django.db import transaction
from celery.exceptions import Retry
from django_redis import get_redis_connection

from apps.monitor_registry.models import MonitorRegistry, DepartmentEmailLog
from apps.monitor_registry.tasks.email_management import (
    schedule_department_alert_emails,
    send_admin_notification_email,
    check_and_resend_missed_period_end_alerts,
    check_and_resend_missed_overdue_alerts,
    collect_all_projects_by_date,
)
from internal_control.celery import app, CeleryResult

# 配置日志
logger = logging.getLogger(__name__)


@contextmanager
def redis_lock(key, timeout=300):
    """Redis分布式锁上下文管理器"""
    try:
        redis_conn = get_redis_connection("default")
        lock_key = f"celery_lock:{key}"

        # 尝试获取锁
        if redis_conn.set(lock_key, "locked", nx=True, ex=timeout):
            try:
                yield
            finally:
                redis_conn.delete(lock_key)
        else:
            raise Exception(f"无法获取锁: {key}，可能有其他实例正在执行")
    except Exception as e:
        # Redis连接失败时，记录警告但不阻止任务执行
        logger.warning(f"Redis分布式锁失败，继续执行任务: {str(e)}")
        yield


@app.task(bind=True, max_retries=3, default_retry_delay=60)
def check_monitor_cycle_and_send_alert(self):
    """
    检查监控周期并发送提醒邮件 - 使用新的按部门分组发送逻辑
    根据monitor_cycle的值进行不同的检查：
    - 每月监控：月底触发提醒邮件，11日检查逾期
    - 季度监控：季度末触发提醒邮件，季度后11日检查逾期
    - 半年监控：半年末触发提醒邮件，半年后11日检查逾期
    - 年度监控：年末触发提醒邮件，次年11日检查逾期

    补发逻辑：
    - 1-10日：检查并补发上一期期末提醒邮件
    - 12-15日：检查并补发11日逾期提醒邮件
    """
    start_time = time.time()

    try:
        # 使用分布式锁防止并发执行
        with redis_lock("check_monitor_cycle_and_send_alert"):
            today = datetime.now().date()
            current_day = today.day

            logger.info(f"开始执行监控周期检查任务: {today}")

            # 性能监控点1：数据库查询
            query_start = time.time()

            # 获取所有启用的监控记录 - 添加数据库异常处理
            try:
                monitor_registries = MonitorRegistry.objects.filter(
                    monitor_switch=True, monitor_cycle__isnull=False
                ).select_related("monitor_cycle", "responsible_department")

                registry_count = monitor_registries.count()
                query_time = time.time() - query_start

                if query_time > 5:  # 查询超过5秒告警
                    logger.warning(f"数据库查询耗时过长: {query_time:.2f}秒，记录数: {registry_count}")

                # 添加数据验证 - 检查是否有启用的监控项目
                if registry_count == 0:
                    logger.info("没有启用的监控项目，跳过处理")
                    return CeleryResult.success("check_monitor_cycle_and_send_alert")

            except Exception as e:
                logger.error(f"查询监控记录失败: {str(e)}")
                # 数据库连接问题，重试
                if "database" in str(e).lower() or "connection" in str(e).lower():
                    raise self.retry(countdown=60, exc=e)
                else:
                    return CeleryResult.fail("check_monitor_cycle_and_send_alert", f"数据库查询失败: {str(e)}")

        # 清理当天发送状态为待发送和失败的邮件记录（避免删除正在发送中的记录）
        try:
            deleted_count, _ = DepartmentEmailLog.objects.filter(
                send_date=today,
                send_status__in=["PENDING", "FAILED"]  # 只删除待发送和失败的记录，不删除SENDING状态
            ).delete()

            if deleted_count > 0:
                logger.info(f"清理当天未成功发送的邮件记录: {deleted_count} 条")
            else:
                logger.info("无需清理当天邮件记录")
        except Exception as e:
            logger.error(f"清理当天邮件记录失败: {str(e)}")
            # 清理失败不应该影响主要业务逻辑，继续执行

        # 1-10日：检查并补发上一期期末提醒邮件 - 增强错误处理
        if 1 <= current_day <= 10:
            try:
                resend_count = check_and_resend_missed_period_end_alerts(monitor_registries, today)
                if resend_count > 0:
                    logger.info(f"补发上一期期末提醒邮件完成，成功补发给 {resend_count} 个部门")
                else:
                    logger.info("无需补发上一期期末提醒邮件")
            except Exception as e:
                logger.error(f"补发上一期期末提醒邮件失败: {str(e)}")
                # 补发失败不影响正常发送，继续执行

        # 12-15日：检查并补发11日逾期提醒邮件 - 增强错误处理
        if 12 <= current_day <= 15:
            try:
                resend_count = check_and_resend_missed_overdue_alerts(monitor_registries, today)
                if resend_count > 0:
                    logger.info(f"补发11日逾期提醒邮件完成，成功补发给 {resend_count} 个部门")
                else:
                    logger.info("无需补发11日逾期提醒邮件")
            except Exception as e:
                logger.error(f"补发11日逾期提醒邮件失败: {str(e)}")
                # 补发失败不影响正常发送，继续执行

        # 统一收集项目数据，避免重复查询和逻辑
        projects_data = collect_all_projects_by_date(monitor_registries, today)

        # 正常发送逻辑 - 期末提醒邮件
        if projects_data['alert_projects']:
            alert_success_count = schedule_department_alert_emails(projects_data['alert_projects'], "ALERT")
            logger.info(f"期末提醒邮件发送完成，成功发送给 {alert_success_count} 个部门")

        # 正常发送逻辑 - 逾期提醒邮件
        if projects_data['overdue_projects']:
            overdue_success_count = schedule_department_alert_emails(projects_data['overdue_projects'], "OVERDUE")
            logger.info(f"逾期提醒邮件发送完成，成功发送给 {overdue_success_count} 个部门")

            # 在所有逾期检查完成后，发送管理员汇总通知 - 使用已收集的数据
            if current_day == 11 and projects_data['admin_overdue_projects']:
                try:
                    admin_success_count = send_admin_notification_email(projects_data['admin_overdue_projects'])
                    logger.info(f"管理员通知邮件发送完成，成功发送给 {admin_success_count} 位管理员")
                except Exception as e:
                    logger.error(f"发送管理员通知邮件失败: {str(e)}")
            elif current_day == 11:
                logger.info("没有逾期项目，无需发送管理员通知邮件")

            # 总体性能监控
            total_time = time.time() - start_time
            logger.info(f"任务执行完成，总耗时: {total_time:.2f}秒，处理记录数: {registry_count}")

            if total_time > 120:  # 超过2分钟告警
                logger.warning(f"任务执行时间过长: {total_time:.2f}秒")

            return CeleryResult.success("check_monitor_cycle_and_send_alert")

    except Retry:
        # 重试异常，直接抛出
        raise
    except Exception as e:
        execution_time = time.time() - start_time
        logger.error(f"任务执行失败，耗时: {execution_time:.2f}秒，错误: {str(e)}")

        # 根据错误类型决定是否重试
        error_msg = str(e).lower()
        if "database" in error_msg or "connection" in error_msg or "timeout" in error_msg:
            # 数据库相关错误，重试
            logger.warning(f"检测到数据库相关错误，将进行重试: {str(e)}")
            raise self.retry(countdown=60, exc=e)
        else:
            # 其他错误，不重试
            return CeleryResult.fail("check_monitor_cycle_and_send_alert", str(e))
