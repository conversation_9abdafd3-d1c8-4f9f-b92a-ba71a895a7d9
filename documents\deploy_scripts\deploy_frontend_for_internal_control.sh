#!/bin/bash

# 常量定义
PROJECT_FOLDER="frontend_dev_internal_control"
NGINX_FOLDER="/workdir/nginx"
NGINX_HTML_FOLDER="$NGINX_FOLDER/html"
NGINX_BINARY="$NGINX_FOLDER/sbin/nginx"

# 检查项目文件夹是否存在
if [ ! -d "$PROJECT_FOLDER" ]; then
    echo "项目文件夹 $PROJECT_FOLDER 不存在"
    exit 1
fi

# 检查 Nginx HTML 文件夹是否存在
if [ ! -d "$NGINX_HTML_FOLDER" ]; then
    echo "Nginx HTML 文件夹不存在"
    exit 1
fi

# 如果 Nginx HTML 文件夹中已经存在同名的项目文件夹，则按日期重命名
if [ -d "$NGINX_HTML_FOLDER/$(basename "$PROJECT_FOLDER")" ]; then
    CURRENT_DATE=$(date +"%Y-%m-%d_%H-%M-%S")
    mv "$NGINX_HTML_FOLDER/$(basename "$PROJECT_FOLDER")" "$NGINX_HTML_FOLDER/$(basename "$PROJECT_FOLDER")-$CURRENT_DATE"
    echo "已将 $(basename "$PROJECT_FOLDER") 重命名为 $(basename "$PROJECT_FOLDER")-$CURRENT_DATE"
fi

# 将当前目录下的项目文件夹转移到 Nginx 的 HTML 文件夹中
mv "$PROJECT_FOLDER" "$NGINX_HTML_FOLDER/"

# 重启 Nginx
echo "重启 Nginx..."

cd "$NGINX_FOLDER"
if ! $NGINX_BINARY -s reload; then
    echo "Nginx 重启失败"
    exit 1
fi

echo "Nginx 重启完成"