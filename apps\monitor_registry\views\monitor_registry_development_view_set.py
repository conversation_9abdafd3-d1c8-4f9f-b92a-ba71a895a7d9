#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : monitor_registry_development_view_set.py
<AUTHOR> JT_DA
@Date     : 2025/04/18
@File_Desc: 监控列表开展情况视图集
"""

from rest_framework import viewsets
from django.db import transaction
from rest_framework.exceptions import ValidationError
from rest_framework.filters import SearchFilter
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.decorators import action

from apps.monitor_registry.models import MonitorRegistryDevelopment
from apps.monitor_registry.serializers import MonitorRegistryDevelopmentSerializer
from utils.pagination import MyPageNumberPagination
from utils.ajax_result import AjaxResult
from utils.permission_helper import permission_required


class BaseMonitorRegistryDevelopmentViewSet(viewsets.ModelViewSet):
    queryset = MonitorRegistryDevelopment.objects.all().order_by("-development_date")
    serializer_class = MonitorRegistryDevelopmentSerializer
    pagination_class = MyPageNumberPagination
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_fields = ["monitor_registry"]

    def get_queryset(self):
        queryset = super().get_queryset()
        # 确保请求中包含monitor_registry参数
        monitor_registry_id = self.request.query_params.get("monitor_registry", None)
        if monitor_registry_id is None:
            raise ValidationError({"monitor_registry": "监控列表ID不能为空"})
        return queryset.filter(monitor_registry_id=monitor_registry_id)


class MonitorRegistryDevelopmentViewSet(BaseMonitorRegistryDevelopmentViewSet):
    @permission_required(per="内控风险监控清单.监控清单_查询")
    def list(self, request, *args, **kwargs):
        """
        监控列表开展情况列表
        """
        return super().list(request, *args, **kwargs)

    @permission_required(per="内控风险监控清单.监控清单_新增")
    def create(self, request, *args, **kwargs):
        """
        监控列表开展情况新增
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        with transaction.atomic():
            serializer.save()

        return AjaxResult.success(msg="新增成功")

    @permission_required(per="内控风险监控清单.监控清单_查询")
    def retrieve(self, request, *args, **kwargs):
        """
        监控列表开展情况详情
        """
        response = super().retrieve(request, *args, **kwargs)
        return AjaxResult.success(data=response.data)

    @permission_required(per="内控风险监控清单.监控清单_修改")
    def update(self, request, *args, **kwargs):
        """
        监控列表开展情况编辑
        """
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        with transaction.atomic():
            serializer.save()

        return AjaxResult.success(msg="编辑成功")

    @permission_required(per="内控风险监控清单.监控清单_删除")
    def destroy(self, request, *args, **kwargs):
        """
        监控列表开展情况删除
        """
        instance = self.get_object()
        instance.delete()
        return AjaxResult.success(msg="删除成功")

    @permission_required(per="内控风险监控清单.监控清单_新增")
    @action(detail=False, methods=["post"])
    def batch_create_or_update(self, request, *args, **kwargs):
        """
        监控列表开展情况批量新增或更新

        参数格式:
        {
            "items": [
                {
                    "id": 1,  # 如存在ID则更新，不存在则新增
                    "monitor_registry": 1,
                    "development_date": "2025-04-20",
                    "development_content": "开展内容1"
                },
                {
                    "monitor_registry": 1,
                    "development_date": "2025-04-21",
                    "development_content": "开展内容2"
                }
            ]
        }
        """
        data = request.data
        items = data.get("items", [])

        if not items:
            return AjaxResult.fail(msg="批量操作数据不能为空")

        create_items = []
        update_items = []

        # 分离需要创建和需要更新的记录
        for item in items:
            if "id" in item and item["id"]:
                update_items.append(item)
            else:
                create_items.append(item)

        # 确保所有记录都属于同一个monitor_registry
        monitor_registry_ids = set()
        for item in items:
            if "monitor_registry" in item and item["monitor_registry"]:
                monitor_registry_ids.add(item["monitor_registry"])

        if len(monitor_registry_ids) != 1:
            return AjaxResult.fail(msg="批量操作的所有记录必须属于同一个监控列表")

        monitor_registry_id = list(monitor_registry_ids)[0]
        created_count = 0
        updated_count = 0
        deleted_count = 0
        processed_ids = []  # 用于存储所有已处理的记录ID

        try:
            with transaction.atomic():
                # 处理新增记录
                for item in create_items:
                    serializer = self.get_serializer(data=item)
                    serializer.is_valid(raise_exception=True)
                    instance = serializer.save()
                    processed_ids.append(instance.id)
                    created_count += 1

                # 处理更新记录
                for item in update_items:
                    instance = self.queryset.filter(id=item["id"]).first()
                    if not instance:
                        raise ValidationError(f"ID为{item['id']}的记录不存在")

                    serializer = self.get_serializer(instance, data=item, partial=True)
                    serializer.is_valid(raise_exception=True)
                    serializer.save()
                    processed_ids.append(instance.id)
                    updated_count += 1

                # 删除不在processed_ids中的同一个monitor_registry的记录
                to_delete = MonitorRegistryDevelopment.objects.filter(monitor_registry_id=monitor_registry_id).exclude(
                    id__in=processed_ids
                )

                deleted_count = to_delete.count()
                to_delete.delete()

            return AjaxResult.success(
                msg=f"批量操作成功，新增{created_count}条，更新{updated_count}条，删除{deleted_count}条",
                data={
                    "created_count": created_count,
                    "updated_count": updated_count,
                    "deleted_count": deleted_count,
                    "processed_ids": processed_ids,
                },
            )
        except ValidationError as e:
            return AjaxResult.fail(msg=f"数据验证失败: {str(e)}", data=e.detail)
        except Exception as e:
            return AjaxResult.fail(msg=f"批量操作失败: {str(e)}")
